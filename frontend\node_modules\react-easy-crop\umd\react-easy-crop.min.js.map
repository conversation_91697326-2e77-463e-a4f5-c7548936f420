{"version": 3, "file": "react-easy-crop.min.js", "sources": ["../../src/helpers.ts", "../../src/Cropper.tsx"], "sourcesContent": ["import { Area, MediaSize, Point, Size } from './types'\n\n/**\n * Compute the dimension of the crop area based on media size,\n * aspect ratio and optionally rotation\n */\nexport function getCropSize(\n  mediaWidth: number,\n  mediaHeight: number,\n  containerWidth: number,\n  containerHeight: number,\n  aspect: number,\n  rotation = 0\n): Size {\n  const { width, height } = rotateSize(mediaWidth, mediaHeight, rotation)\n  const fittingWidth = Math.min(width, containerWidth)\n  const fittingHeight = Math.min(height, containerHeight)\n\n  if (fittingWidth > fittingHeight * aspect) {\n    return {\n      width: fittingHeight * aspect,\n      height: fittingHeight,\n    }\n  }\n\n  return {\n    width: fittingWidth,\n    height: fittingWidth / aspect,\n  }\n}\n\n/**\n * Compute media zoom.\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\n */\nexport function getMediaZoom(mediaSize: MediaSize) {\n  // Take the axis with more pixels to improve accuracy\n  return mediaSize.width > mediaSize.height\n    ? mediaSize.width / mediaSize.naturalWidth\n    : mediaSize.height / mediaSize.naturalHeight\n}\n\n/**\n * Ensure a new media position stays in the crop area.\n */\nexport function restrictPosition(\n  position: Point,\n  mediaSize: Size,\n  cropSize: Size,\n  zoom: number,\n  rotation = 0\n): Point {\n  const { width, height } = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  return {\n    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n    y: restrictPositionCoord(position.y, height, cropSize.height, zoom),\n  }\n}\n\nfunction restrictPositionCoord(\n  position: number,\n  mediaSize: number,\n  cropSize: number,\n  zoom: number\n): number {\n  const maxPosition = (mediaSize * zoom) / 2 - cropSize / 2\n\n  return clamp(position, -maxPosition, maxPosition)\n}\n\nexport function getDistanceBetweenPoints(pointA: Point, pointB: Point) {\n  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2))\n}\n\nexport function getRotationBetweenPoints(pointA: Point, pointB: Point) {\n  return (Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180) / Math.PI\n}\n\n/**\n * Compute the output cropped area of the media in percentages and pixels.\n * x/y are the top-left coordinates on the src media\n */\nexport function computeCroppedArea(\n  crop: Point,\n  mediaSize: MediaSize,\n  cropSize: Size,\n  aspect: number,\n  zoom: number,\n  rotation = 0,\n  restrictPosition = true\n): { croppedAreaPercentages: Area; croppedAreaPixels: Area } {\n  // if the media is rotated by the user, we cannot limit the position anymore\n  // as it might need to be negative.\n  const limitAreaFn = restrictPosition ? limitArea : noOp\n\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  // calculate the crop area in percentages\n  // in the rotated space\n  const croppedAreaPercentages = {\n    x: limitAreaFn(\n      100,\n      (((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width) *\n        100\n    ),\n    y: limitAreaFn(\n      100,\n      (((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) /\n        mediaBBoxSize.height) *\n        100\n    ),\n    width: limitAreaFn(100, ((cropSize.width / mediaBBoxSize.width) * 100) / zoom),\n    height: limitAreaFn(100, ((cropSize.height / mediaBBoxSize.height) * 100) / zoom),\n  }\n\n  // we compute the pixels size naively\n  const widthInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.width,\n      (croppedAreaPercentages.width * mediaNaturalBBoxSize.width) / 100\n    )\n  )\n  const heightInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.height,\n      (croppedAreaPercentages.height * mediaNaturalBBoxSize.height) / 100\n    )\n  )\n  const isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect\n\n  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n  // thus we want to compute the width from the height and aspect for accuracy.\n  // Otherwise, we compute the height from width and aspect.\n  const sizePixels = isImgWiderThanHigh\n    ? {\n        width: Math.round(heightInPixels * aspect),\n        height: heightInPixels,\n      }\n    : {\n        width: widthInPixels,\n        height: Math.round(widthInPixels / aspect),\n      }\n\n  const croppedAreaPixels = {\n    ...sizePixels,\n    x: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.width - sizePixels.width,\n        (croppedAreaPercentages.x * mediaNaturalBBoxSize.width) / 100\n      )\n    ),\n    y: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.height - sizePixels.height,\n        (croppedAreaPercentages.y * mediaNaturalBBoxSize.height) / 100\n      )\n    ),\n  }\n\n  return { croppedAreaPercentages, croppedAreaPixels }\n}\n\n/**\n * Ensure the returned value is between 0 and max\n */\nfunction limitArea(max: number, value: number): number {\n  return Math.min(max, Math.max(0, value))\n}\n\nfunction noOp(_max: number, value: number) {\n  return value\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPercentages.\n */\nexport function getInitialCropFromCroppedAreaPercentages(\n  croppedAreaPercentages: Area,\n  mediaSize: MediaSize,\n  rotation: number,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n) {\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  // This is the inverse process of computeCroppedArea\n  const zoom = clamp(\n    (cropSize.width / mediaBBoxSize.width) * (100 / croppedAreaPercentages.width),\n    minZoom,\n    maxZoom\n  )\n\n  const crop = {\n    x:\n      (zoom * mediaBBoxSize.width) / 2 -\n      cropSize.width / 2 -\n      mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n    y:\n      (zoom * mediaBBoxSize.height) / 2 -\n      cropSize.height / 2 -\n      mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100),\n  }\n\n  return { crop, zoom }\n}\n\n/**\n * Compute zoom from the croppedAreaPixels\n */\nfunction getZoomFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  cropSize: Size\n): number {\n  const mediaZoom = getMediaZoom(mediaSize)\n\n  return cropSize.height > cropSize.width\n    ? cropSize.height / (croppedAreaPixels.height * mediaZoom)\n    : cropSize.width / (croppedAreaPixels.width * mediaZoom)\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPixels\n */\nexport function getInitialCropFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  rotation = 0,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n): { crop: Point; zoom: number } {\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  const zoom = clamp(\n    getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize),\n    minZoom,\n    maxZoom\n  )\n\n  const cropZoom =\n    cropSize.height > cropSize.width\n      ? cropSize.height / croppedAreaPixels.height\n      : cropSize.width / croppedAreaPixels.width\n\n  const crop = {\n    x:\n      ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n    y:\n      ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) *\n      cropZoom,\n  }\n  return { crop, zoom }\n}\n\n/**\n * Return the point that is the center of point a and b\n */\nexport function getCenter(a: Point, b: Point): Point {\n  return {\n    x: (b.x + a.x) / 2,\n    y: (b.y + a.y) / 2,\n  }\n}\n\nexport function getRadianAngle(degreeValue: number) {\n  return (degreeValue * Math.PI) / 180\n}\n\n/**\n * Returns the new bounding area of a rotated rectangle.\n */\nexport function rotateSize(width: number, height: number, rotation: number): Size {\n  const rotRad = getRadianAngle(rotation)\n\n  return {\n    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height),\n  }\n}\n\n/**\n * Clamp value between min and max\n */\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, min), max)\n}\n\n/**\n * Combine multiple class names into a single string.\n */\nexport function classNames(...args: (boolean | string | number | undefined | void | null)[]) {\n  return args\n    .filter((value) => {\n      if (typeof value === 'string' && value.length > 0) {\n        return true\n      }\n\n      return false\n    })\n    .join(' ')\n    .trim()\n}\n", "import * as React from 'react'\nimport normalizeWheel from 'normalize-wheel'\nimport { Area, MediaSize, Point, Size, VideoSrc } from './types'\nimport {\n  getCropSize,\n  restrictPosition,\n  getDistanceBetweenPoints,\n  getRotationBetweenPoints,\n  computeCroppedArea,\n  getCenter,\n  getInitialCropFromCroppedAreaPixels,\n  getInitialCropFromCroppedAreaPercentages,\n  classNames,\n  clamp,\n} from './helpers'\nimport cssStyles from './styles.css'\n\nexport type CropperProps = {\n  image?: string\n  video?: string | VideoSrc[]\n  transform?: string\n  crop: Point\n  zoom: number\n  rotation: number\n  aspect: number\n  minZoom: number\n  maxZoom: number\n  cropShape: 'rect' | 'round'\n  cropSize?: Size\n  objectFit?: 'contain' | 'cover' | 'horizontal-cover' | 'vertical-cover'\n  showGrid?: boolean\n  zoomSpeed: number\n  zoomWithScroll?: boolean\n  roundCropAreaPixels?: boolean\n  onCropChange: (location: Point) => void\n  onZoomChange?: (zoom: number) => void\n  onRotationChange?: (rotation: number) => void\n  onCropComplete?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropAreaChange?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropSizeChange?: (cropSize: Size) => void\n  onInteractionStart?: () => void\n  onInteractionEnd?: () => void\n  onMediaLoaded?: (mediaSize: MediaSize) => void\n  style: {\n    containerStyle?: React.CSSProperties\n    mediaStyle?: React.CSSProperties\n    cropAreaStyle?: React.CSSProperties\n  }\n  classes: {\n    containerClassName?: string\n    mediaClassName?: string\n    cropAreaClassName?: string\n  }\n  restrictPosition: boolean\n  mediaProps: React.ImgHTMLAttributes<HTMLElement> | React.VideoHTMLAttributes<HTMLElement>\n  cropperProps: React.HTMLAttributes<HTMLDivElement>\n  disableAutomaticStylesInjection?: boolean\n  initialCroppedAreaPixels?: Area\n  initialCroppedAreaPercentages?: Area\n  onTouchRequest?: (e: React.TouchEvent<HTMLDivElement>) => boolean\n  onWheelRequest?: (e: WheelEvent) => boolean\n  setCropperRef?: (ref: React.RefObject<HTMLDivElement>) => void\n  setImageRef?: (ref: React.RefObject<HTMLImageElement>) => void\n  setVideoRef?: (ref: React.RefObject<HTMLVideoElement>) => void\n  setMediaSize?: (size: MediaSize) => void\n  setCropSize?: (size: Size) => void\n  nonce?: string\n  keyboardStep: number\n}\n\ntype State = {\n  cropSize: Size | null\n  hasWheelJustStarted: boolean\n  mediaObjectFit: String | undefined\n}\n\nconst MIN_ZOOM = 1\nconst MAX_ZOOM = 3\nconst KEYBOARD_STEP = 1\n\ntype GestureEvent = UIEvent & {\n  rotation: number\n  scale: number\n  clientX: number\n  clientY: number\n}\n\nclass Cropper extends React.Component<CropperProps, State> {\n  static defaultProps = {\n    zoom: 1,\n    rotation: 0,\n    aspect: 4 / 3,\n    maxZoom: MAX_ZOOM,\n    minZoom: MIN_ZOOM,\n    cropShape: 'rect' as const,\n    objectFit: 'contain' as const,\n    showGrid: true,\n    style: {},\n    classes: {},\n    mediaProps: {},\n    cropperProps: {},\n    zoomSpeed: 1,\n    restrictPosition: true,\n    zoomWithScroll: true,\n    keyboardStep: KEYBOARD_STEP,\n  }\n\n  cropperRef: React.RefObject<HTMLDivElement> = React.createRef()\n  imageRef: React.RefObject<HTMLImageElement> = React.createRef()\n  videoRef: React.RefObject<HTMLVideoElement> = React.createRef()\n  containerPosition: Point = { x: 0, y: 0 }\n  containerRef: HTMLDivElement | null = null\n  styleRef: HTMLStyleElement | null = null\n  containerRect: DOMRect | null = null\n  mediaSize: MediaSize = { width: 0, height: 0, naturalWidth: 0, naturalHeight: 0 }\n  dragStartPosition: Point = { x: 0, y: 0 }\n  dragStartCrop: Point = { x: 0, y: 0 }\n  gestureZoomStart = 0\n  gestureRotationStart = 0\n  isTouching = false\n  lastPinchDistance = 0\n  lastPinchRotation = 0\n  rafDragTimeout: number | null = null\n  rafPinchTimeout: number | null = null\n  wheelTimer: number | null = null\n  currentDoc: Document | null = typeof document !== 'undefined' ? document : null\n  currentWindow: Window | null = typeof window !== 'undefined' ? window : null\n  resizeObserver: ResizeObserver | null = null\n\n  state: State = {\n    cropSize: null,\n    hasWheelJustStarted: false,\n    mediaObjectFit: undefined,\n  }\n\n  componentDidMount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (this.containerRef) {\n      if (this.containerRef.ownerDocument) {\n        this.currentDoc = this.containerRef.ownerDocument\n      }\n      if (this.currentDoc.defaultView) {\n        this.currentWindow = this.currentDoc.defaultView\n      }\n\n      this.initResizeObserver()\n      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n      if (typeof window.ResizeObserver === 'undefined') {\n        this.currentWindow.addEventListener('resize', this.computeSizes)\n      }\n      this.props.zoomWithScroll &&\n        this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n      this.containerRef.addEventListener('gesturestart', this.onGestureStart as EventListener)\n    }\n\n    this.currentDoc.addEventListener('scroll', this.onScroll)\n\n    if (!this.props.disableAutomaticStylesInjection) {\n      this.styleRef = this.currentDoc.createElement('style')\n      this.styleRef.setAttribute('type', 'text/css')\n      if (this.props.nonce) {\n        this.styleRef.setAttribute('nonce', this.props.nonce)\n      }\n      this.styleRef.innerHTML = cssStyles\n      this.currentDoc.head.appendChild(this.styleRef)\n    }\n\n    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n    if (this.imageRef.current && this.imageRef.current.complete) {\n      this.onMediaLoad()\n    }\n\n    // set image and video refs in the parent if the callbacks exist\n    if (this.props.setImageRef) {\n      this.props.setImageRef(this.imageRef)\n    }\n\n    if (this.props.setVideoRef) {\n      this.props.setVideoRef(this.videoRef)\n    }\n\n    if (this.props.setCropperRef) {\n      this.props.setCropperRef(this.cropperRef)\n    }\n  }\n\n  componentWillUnmount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (typeof window.ResizeObserver === 'undefined') {\n      this.currentWindow.removeEventListener('resize', this.computeSizes)\n    }\n    this.resizeObserver?.disconnect()\n    if (this.containerRef) {\n      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari)\n    }\n\n    if (this.styleRef) {\n      this.styleRef.parentNode?.removeChild(this.styleRef)\n    }\n\n    this.cleanEvents()\n    this.props.zoomWithScroll && this.clearScrollEvent()\n  }\n\n  componentDidUpdate(prevProps: CropperProps) {\n    if (prevProps.rotation !== this.props.rotation) {\n      this.computeSizes()\n      this.recomputeCropPosition()\n    } else if (prevProps.aspect !== this.props.aspect) {\n      this.computeSizes()\n    } else if (prevProps.objectFit !== this.props.objectFit) {\n      this.computeSizes()\n    } else if (prevProps.zoom !== this.props.zoom) {\n      this.recomputeCropPosition()\n    } else if (\n      prevProps.cropSize?.height !== this.props.cropSize?.height ||\n      prevProps.cropSize?.width !== this.props.cropSize?.width\n    ) {\n      this.computeSizes()\n    } else if (\n      prevProps.crop?.x !== this.props.crop?.x ||\n      prevProps.crop?.y !== this.props.crop?.y\n    ) {\n      this.emitCropAreaChange()\n    }\n    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n      this.props.zoomWithScroll\n        ? this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n        : this.clearScrollEvent()\n    }\n    if (prevProps.video !== this.props.video) {\n      this.videoRef.current?.load()\n    }\n\n    const objectFit = this.getObjectFit()\n    if (objectFit !== this.state.mediaObjectFit) {\n      this.setState({ mediaObjectFit: objectFit }, this.computeSizes)\n    }\n  }\n\n  initResizeObserver = () => {\n    if (typeof window.ResizeObserver === 'undefined' || !this.containerRef) {\n      return\n    }\n    let isFirstResize = true\n    this.resizeObserver = new window.ResizeObserver((entries) => {\n      if (isFirstResize) {\n        isFirstResize = false // observe() is called on mount, we don't want to trigger a recompute on mount\n        return\n      }\n      this.computeSizes()\n    })\n    this.resizeObserver.observe(this.containerRef)\n  }\n\n  // this is to prevent Safari on iOS >= 10 to zoom the page\n  preventZoomSafari = (e: Event) => e.preventDefault()\n\n  cleanEvents = () => {\n    if (!this.currentDoc) return\n    this.currentDoc.removeEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.removeEventListener('mouseup', this.onDragStopped)\n    this.currentDoc.removeEventListener('touchmove', this.onTouchMove)\n    this.currentDoc.removeEventListener('touchend', this.onDragStopped)\n    this.currentDoc.removeEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.removeEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.currentDoc.removeEventListener('scroll', this.onScroll)\n  }\n\n  clearScrollEvent = () => {\n    if (this.containerRef) this.containerRef.removeEventListener('wheel', this.onWheel)\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n  }\n\n  onMediaLoad = () => {\n    const cropSize = this.computeSizes()\n\n    if (cropSize) {\n      this.emitCropData()\n      this.setInitialCrop(cropSize)\n    }\n\n    if (this.props.onMediaLoaded) {\n      this.props.onMediaLoaded(this.mediaSize)\n    }\n  }\n\n  setInitialCrop = (cropSize: Size) => {\n    if (this.props.initialCroppedAreaPercentages) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPercentages(\n        this.props.initialCroppedAreaPercentages,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    } else if (this.props.initialCroppedAreaPixels) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPixels(\n        this.props.initialCroppedAreaPixels,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    }\n  }\n\n  getAspect() {\n    const { cropSize, aspect } = this.props\n    if (cropSize) {\n      return cropSize.width / cropSize.height\n    }\n    return aspect\n  }\n\n  getObjectFit() {\n    if (this.props.objectFit === 'cover') {\n      const mediaRef = this.imageRef.current || this.videoRef.current\n\n      if (mediaRef && this.containerRef) {\n        this.containerRect = this.containerRef.getBoundingClientRect()\n        const containerAspect = this.containerRect.width / this.containerRect.height\n        const naturalWidth =\n          this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n        const naturalHeight =\n          this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n        const mediaAspect = naturalWidth / naturalHeight\n\n        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover'\n      }\n      return 'horizontal-cover'\n    }\n\n    return this.props.objectFit\n  }\n\n  computeSizes = () => {\n    const mediaRef = this.imageRef.current || this.videoRef.current\n\n    if (mediaRef && this.containerRef) {\n      this.containerRect = this.containerRef.getBoundingClientRect()\n      this.saveContainerPosition()\n      const containerAspect = this.containerRect.width / this.containerRect.height\n      const naturalWidth =\n        this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n      const naturalHeight =\n        this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n      const isMediaScaledDown =\n        mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight\n      const mediaAspect = naturalWidth / naturalHeight\n\n      // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n      // as the values they report are rounded. That will result in precision losses\n      // when calculating zoom. We use the fact that the media is positionned relative\n      // to the container. That allows us to use the container's dimensions\n      // and natural aspect ratio of the media to calculate accurate media size.\n      // However, for this to work, the container should not be rotated\n      let renderedMediaSize: Size\n\n      if (isMediaScaledDown) {\n        switch (this.state.mediaObjectFit) {\n          default:\n          case 'contain':\n            renderedMediaSize =\n              containerAspect > mediaAspect\n                ? {\n                    width: this.containerRect.height * mediaAspect,\n                    height: this.containerRect.height,\n                  }\n                : {\n                    width: this.containerRect.width,\n                    height: this.containerRect.width / mediaAspect,\n                  }\n            break\n          case 'horizontal-cover':\n            renderedMediaSize = {\n              width: this.containerRect.width,\n              height: this.containerRect.width / mediaAspect,\n            }\n            break\n          case 'vertical-cover':\n            renderedMediaSize = {\n              width: this.containerRect.height * mediaAspect,\n              height: this.containerRect.height,\n            }\n            break\n        }\n      } else {\n        renderedMediaSize = {\n          width: mediaRef.offsetWidth,\n          height: mediaRef.offsetHeight,\n        }\n      }\n\n      this.mediaSize = {\n        ...renderedMediaSize,\n        naturalWidth,\n        naturalHeight,\n      }\n\n      // set media size in the parent\n      if (this.props.setMediaSize) {\n        this.props.setMediaSize(this.mediaSize)\n      }\n\n      const cropSize = this.props.cropSize\n        ? this.props.cropSize\n        : getCropSize(\n            this.mediaSize.width,\n            this.mediaSize.height,\n            this.containerRect.width,\n            this.containerRect.height,\n            this.props.aspect,\n            this.props.rotation\n          )\n\n      if (\n        this.state.cropSize?.height !== cropSize.height ||\n        this.state.cropSize?.width !== cropSize.width\n      ) {\n        this.props.onCropSizeChange && this.props.onCropSizeChange(cropSize)\n      }\n      this.setState({ cropSize }, this.recomputeCropPosition)\n      // pass crop size to parent\n      if (this.props.setCropSize) {\n        this.props.setCropSize(cropSize)\n      }\n\n      return cropSize\n    }\n  }\n\n  saveContainerPosition = () => {\n    if (this.containerRef) {\n      const bounds = this.containerRef.getBoundingClientRect()\n      this.containerPosition = { x: bounds.left, y: bounds.top }\n    }\n  }\n\n  static getMousePoint = (e: MouseEvent | React.MouseEvent | GestureEvent) => ({\n    x: Number(e.clientX),\n    y: Number(e.clientY),\n  })\n\n  static getTouchPoint = (touch: Touch | React.Touch) => ({\n    x: Number(touch.clientX),\n    y: Number(touch.clientY),\n  })\n\n  onMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.addEventListener('mouseup', this.onDragStopped)\n    this.saveContainerPosition()\n    this.onDragStart(Cropper.getMousePoint(e))\n  }\n\n  onMouseMove = (e: MouseEvent) => this.onDrag(Cropper.getMousePoint(e))\n\n  onScroll = (e: Event) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.saveContainerPosition()\n  }\n\n  onTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {\n    if (!this.currentDoc) return\n    this.isTouching = true\n    if (this.props.onTouchRequest && !this.props.onTouchRequest(e)) {\n      return\n    }\n\n    this.currentDoc.addEventListener('touchmove', this.onTouchMove, { passive: false }) // iOS 11 now defaults to passive: true\n    this.currentDoc.addEventListener('touchend', this.onDragStopped)\n\n    this.saveContainerPosition()\n\n    if (e.touches.length === 2) {\n      this.onPinchStart(e)\n    } else if (e.touches.length === 1) {\n      this.onDragStart(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onTouchMove = (e: TouchEvent) => {\n    // Prevent whole page from scrolling on iOS.\n    e.preventDefault()\n    if (e.touches.length === 2) {\n      this.onPinchMove(e)\n    } else if (e.touches.length === 1) {\n      this.onDrag(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onGestureStart = (e: GestureEvent) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.addEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.gestureZoomStart = this.props.zoom\n    this.gestureRotationStart = this.props.rotation\n  }\n\n  onGestureChange = (e: GestureEvent) => {\n    e.preventDefault()\n    if (this.isTouching) {\n      // this is to avoid conflict between gesture and touch events\n      return\n    }\n\n    const point = Cropper.getMousePoint(e)\n    const newZoom = this.gestureZoomStart - 1 + e.scale\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n    if (this.props.onRotationChange) {\n      const newRotation = this.gestureRotationStart + e.rotation\n      this.props.onRotationChange(newRotation)\n    }\n  }\n\n  onGestureEnd = (e: GestureEvent) => {\n    this.cleanEvents()\n  }\n\n  onDragStart = ({ x, y }: Point) => {\n    this.dragStartPosition = { x, y }\n    this.dragStartCrop = { ...this.props.crop }\n    this.props.onInteractionStart?.()\n  }\n\n  onDrag = ({ x, y }: Point) => {\n    if (!this.currentWindow) return\n    if (this.rafDragTimeout) this.currentWindow.cancelAnimationFrame(this.rafDragTimeout)\n\n    this.rafDragTimeout = this.currentWindow.requestAnimationFrame(() => {\n      if (!this.state.cropSize) return\n      if (x === undefined || y === undefined) return\n      const offsetX = x - this.dragStartPosition.x\n      const offsetY = y - this.dragStartPosition.y\n      const requestedPosition = {\n        x: this.dragStartCrop.x + offsetX,\n        y: this.dragStartCrop.y + offsetY,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            this.props.zoom,\n            this.props.rotation\n          )\n        : requestedPosition\n      this.props.onCropChange(newPosition)\n    })\n  }\n\n  onDragStopped = () => {\n    this.isTouching = false\n    this.cleanEvents()\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  onPinchStart(e: React.TouchEvent<HTMLDivElement>) {\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB)\n    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB)\n    this.onDragStart(getCenter(pointA, pointB))\n  }\n\n  onPinchMove(e: TouchEvent) {\n    if (!this.currentDoc || !this.currentWindow) return\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    const center = getCenter(pointA, pointB)\n    this.onDrag(center)\n\n    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout)\n    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(() => {\n      const distance = getDistanceBetweenPoints(pointA, pointB)\n      const newZoom = this.props.zoom * (distance / this.lastPinchDistance)\n      this.setNewZoom(newZoom, center, { shouldUpdatePosition: false })\n      this.lastPinchDistance = distance\n\n      const rotation = getRotationBetweenPoints(pointA, pointB)\n      const newRotation = this.props.rotation + (rotation - this.lastPinchRotation)\n      this.props.onRotationChange && this.props.onRotationChange(newRotation)\n      this.lastPinchRotation = rotation\n    })\n  }\n\n  onWheel = (e: WheelEvent) => {\n    if (!this.currentWindow) return\n    if (this.props.onWheelRequest && !this.props.onWheelRequest(e)) {\n      return\n    }\n\n    e.preventDefault()\n    const point = Cropper.getMousePoint(e)\n    const { pixelY } = normalizeWheel(e)\n    const newZoom = this.props.zoom - (pixelY * this.props.zoomSpeed) / 200\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n\n    if (!this.state.hasWheelJustStarted) {\n      this.setState({ hasWheelJustStarted: true }, () => this.props.onInteractionStart?.())\n    }\n\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n    this.wheelTimer = this.currentWindow.setTimeout(\n      () => this.setState({ hasWheelJustStarted: false }, () => this.props.onInteractionEnd?.()),\n      250\n    )\n  }\n\n  getPointOnContainer = ({ x, y }: Point, containerTopLeft: Point): Point => {\n    if (!this.containerRect) {\n      throw new Error('The Cropper is not mounted')\n    }\n    return {\n      x: this.containerRect.width / 2 - (x - containerTopLeft.x),\n      y: this.containerRect.height / 2 - (y - containerTopLeft.y),\n    }\n  }\n\n  getPointOnMedia = ({ x, y }: Point) => {\n    const { crop, zoom } = this.props\n    return {\n      x: (x + crop.x) / zoom,\n      y: (y + crop.y) / zoom,\n    }\n  }\n\n  setNewZoom = (zoom: number, point: Point, { shouldUpdatePosition = true } = {}) => {\n    if (!this.state.cropSize || !this.props.onZoomChange) return\n\n    const newZoom = clamp(zoom, this.props.minZoom, this.props.maxZoom)\n\n    if (shouldUpdatePosition) {\n      const zoomPoint = this.getPointOnContainer(point, this.containerPosition)\n      const zoomTarget = this.getPointOnMedia(zoomPoint)\n      const requestedPosition = {\n        x: zoomTarget.x * newZoom - zoomPoint.x,\n        y: zoomTarget.y * newZoom - zoomPoint.y,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            newZoom,\n            this.props.rotation\n          )\n        : requestedPosition\n\n      this.props.onCropChange(newPosition)\n    }\n    this.props.onZoomChange(newZoom)\n  }\n\n  getCropData = () => {\n    if (!this.state.cropSize) {\n      return null\n    }\n\n    // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n    const restrictedPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n    return computeCroppedArea(\n      restrictedPosition,\n      this.mediaSize,\n      this.state.cropSize,\n      this.getAspect(),\n      this.props.zoom,\n      this.props.rotation,\n      this.props.restrictPosition\n    )\n  }\n\n  emitCropData = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropComplete) {\n      this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels)\n    }\n\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  emitCropAreaChange = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  recomputeCropPosition = () => {\n    if (!this.state.cropSize) return\n\n    const newPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n\n    this.props.onCropChange(newPosition)\n    this.emitCropData()\n  }\n\n  onKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    const { crop, onCropChange, keyboardStep, zoom, rotation } = this.props\n    let step = keyboardStep\n\n    if (!this.state.cropSize) return\n\n    // if the shift key is pressed, reduce the step to allow finer control\n    if (event.shiftKey) {\n      step *= 0.2\n    }\n\n    let newCrop = { ...crop }\n\n    switch (event.key) {\n      case 'ArrowUp':\n        newCrop.y -= step\n        event.preventDefault()\n        break\n      case 'ArrowDown':\n        newCrop.y += step\n        event.preventDefault()\n        break\n      case 'ArrowLeft':\n        newCrop.x -= step\n        event.preventDefault()\n        break\n      case 'ArrowRight':\n        newCrop.x += step\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n\n    if (this.props.restrictPosition) {\n      newCrop = restrictPosition(newCrop, this.mediaSize, this.state.cropSize, zoom, rotation)\n    }\n\n    if (!event.repeat) {\n      this.props.onInteractionStart?.()\n    }\n\n    onCropChange(newCrop)\n  }\n\n  onKeyUp = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    switch (event.key) {\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  render() {\n    const {\n      image,\n      video,\n      mediaProps,\n      cropperProps,\n      transform,\n      crop: { x, y },\n      rotation,\n      zoom,\n      cropShape,\n      showGrid,\n      roundCropAreaPixels,\n      style: { containerStyle, cropAreaStyle, mediaStyle },\n      classes: { containerClassName, cropAreaClassName, mediaClassName },\n    } = this.props\n\n    const objectFit = this.state.mediaObjectFit ?? this.getObjectFit()\n\n    return (\n      <div\n        onMouseDown={this.onMouseDown}\n        onTouchStart={this.onTouchStart}\n        ref={(el) => (this.containerRef = el)}\n        data-testid=\"container\"\n        style={containerStyle}\n        className={classNames('reactEasyCrop_Container', containerClassName)}\n      >\n        {image ? (\n          <img\n            alt=\"\"\n            className={classNames(\n              'reactEasyCrop_Image',\n              objectFit === 'contain' && 'reactEasyCrop_Contain',\n              objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n              objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n              mediaClassName\n            )}\n            {...(mediaProps as React.ImgHTMLAttributes<HTMLElement>)}\n            src={image}\n            ref={this.imageRef}\n            style={{\n              ...mediaStyle,\n              transform:\n                transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n            }}\n            onLoad={this.onMediaLoad}\n          />\n        ) : (\n          video && (\n            <video\n              autoPlay\n              playsInline\n              loop\n              muted={true}\n              className={classNames(\n                'reactEasyCrop_Video',\n                objectFit === 'contain' && 'reactEasyCrop_Contain',\n                objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n                objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n                mediaClassName\n              )}\n              {...mediaProps}\n              ref={this.videoRef}\n              onLoadedMetadata={this.onMediaLoad}\n              style={{\n                ...mediaStyle,\n                transform:\n                  transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n              }}\n              controls={false}\n            >\n              {(Array.isArray(video) ? video : [{ src: video }]).map((item) => (\n                <source key={item.src} {...item} />\n              ))}\n            </video>\n          )\n        )}\n        {this.state.cropSize && (\n          <div\n            ref={this.cropperRef}\n            style={{\n              ...cropAreaStyle,\n              width: roundCropAreaPixels\n                ? Math.round(this.state.cropSize.width)\n                : this.state.cropSize.width,\n              height: roundCropAreaPixels\n                ? Math.round(this.state.cropSize.height)\n                : this.state.cropSize.height,\n            }}\n            tabIndex={0}\n            onKeyDown={this.onKeyDown}\n            onKeyUp={this.onKeyUp}\n            data-testid=\"cropper\"\n            className={classNames(\n              'reactEasyCrop_CropArea',\n              cropShape === 'round' && 'reactEasyCrop_CropAreaRound',\n              showGrid && 'reactEasyCrop_CropAreaGrid',\n              cropAreaClassName\n            )}\n            {...cropperProps}\n          />\n        )}\n      </div>\n    )\n  }\n}\n\nexport default Cropper\n"], "names": ["getCropSize", "mediaWidth", "mediaHeight", "containerWidth", "containerHeight", "aspect", "rotation", "_a", "rotateSize", "width", "height", "fittingWidth", "Math", "min", "fittingHeight", "restrictPosition", "position", "mediaSize", "cropSize", "zoom", "x", "restrictPositionCoord", "y", "maxPosition", "clamp", "getDistanceBetweenPoints", "pointA", "pointB", "sqrt", "pow", "getRotationBetweenPoints", "atan2", "PI", "computeCroppedArea", "crop", "limitAreaFn", "limitArea", "noOp", "mediaBBoxSize", "mediaNaturalBBoxSize", "naturalWidth", "naturalHeight", "croppedAreaPercentages", "widthInPixels", "round", "heightInPixels", "sizePixels", "croppedAreaPixels", "__assign", "max", "value", "_max", "getInitialCropFromCroppedAreaPercentages", "minZoom", "max<PERSON><PERSON>", "getInitialCropFromCroppedAreaPixels", "mediaZoom", "getMediaZoom", "getZoomFromCroppedAreaPixels", "cropZoom", "getCenter", "a", "b", "rotRad", "abs", "cos", "sin", "classNames", "args", "_i", "arguments", "length", "filter", "join", "trim", "C<PERSON>per", "_super", "_this", "apply", "this", "cropperRef", "React", "createRef", "imageRef", "videoRef", "containerPosition", "containerRef", "styleRef", "containerRect", "dragStartPosition", "dragStartCrop", "gestureZoomStart", "gestureRotationStart", "isTouching", "lastPinchDistance", "lastPinchRotation", "rafDragTimeout", "rafPinchTimeout", "wheelTimer", "currentDoc", "document", "currentWindow", "window", "resizeObserver", "state", "hasWheelJustStarted", "mediaObjectFit", "undefined", "initResizeObserver", "ResizeObserver", "isFirstResize", "entries", "computeSizes", "observe", "preventZoomSafari", "e", "preventDefault", "cleanEvents", "removeEventListener", "onMouseMove", "onDragStopped", "onTouchMove", "onGestureChange", "onGestureEnd", "onScroll", "clearScrollEvent", "onWheel", "clearTimeout", "onMediaLoad", "emitCropData", "setInitialCrop", "props", "onMediaLoaded", "initialCroppedAreaPercentages", "onCropChange", "onZoomChange", "initialCroppedAreaPixels", "_b", "mediaRef", "current", "getBoundingClientRect", "saveContainerPosition", "containerAspect", "videoWidth", "_c", "_d", "videoHeight", "mediaAspect", "renderedMediaSize", "offsetWidth", "offsetHeight", "setMediaSize", "_e", "_f", "onCropSizeChange", "setState", "recomputeCropPosition", "setCropSize", "bounds", "left", "top", "onMouseDown", "addEventListener", "onDragStart", "getMousePoint", "onDrag", "onTouchStart", "onTouchRequest", "passive", "touches", "onPinchStart", "getTouchPoint", "onPinchMove", "onGestureStart", "point", "newZoom", "scale", "setNewZoom", "shouldUpdatePosition", "onRotationChange", "newRotation", "onInteractionStart", "cancelAnimationFrame", "requestAnimationFrame", "offsetX", "offsetY", "requestedPosition", "newPosition", "onInteractionEnd", "onWheelRequest", "pixelY", "normalizeWheel", "zoomSpeed", "call", "setTimeout", "getPointOnContainer", "containerTopLeft", "Error", "getPointOnMedia", "zoomPoint", "zoomTarget", "getCropData", "getAspect", "cropData", "onCropComplete", "onCropAreaChange", "emitCropAreaChange", "onKeyDown", "event", "keyboardStep", "step", "shift<PERSON>ey", "newCrop", "key", "repeat", "onKeyUp", "__extends", "prototype", "componentDidMount", "ownerDocument", "defaultView", "zoomWithScroll", "disableAutomaticStylesInjection", "createElement", "setAttribute", "nonce", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "complete", "setImageRef", "setVideoRef", "setCropperRef", "componentWillUnmount", "disconnect", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "componentDidUpdate", "prevProps", "objectFit", "_g", "_h", "video", "_j", "load", "getObjectFit", "center", "distance", "render", "image", "mediaProps", "cropperProps", "transform", "cropShape", "showGrid", "roundCropAreaPixels", "style", "containerStyle", "cropAreaStyle", "mediaStyle", "classes", "containerClassName", "cropAreaClassName", "mediaClassName", "ref", "el", "data-testid", "className", "alt", "src", "concat", "onLoad", "autoPlay", "playsInline", "loop", "muted", "onLoadedMetadata", "controls", "Array", "isArray", "map", "item", "tabIndex", "defaultProps", "Number", "clientX", "clientY", "touch", "Component"], "mappings": "ovBAMgB,SAAAA,EACdC,EACAC,EACAC,EACAC,EACAC,EACAC,QAAA,IAAAA,IAAAA,EAAY,GAEN,IAAAC,EAAoBC,EAAWP,EAAYC,EAAaI,GAAtDG,EAAKF,EAAAE,MAAEC,WACTC,EAAeC,KAAKC,IAAIJ,EAAON,GAC/BW,EAAgBF,KAAKC,IAAIH,EAAQN,GAEvC,OAAIO,EAAeG,EAAgBT,EAC1B,CACLI,MAAOK,EAAgBT,EACvBK,OAAQI,GAIL,CACLL,MAAOE,EACPD,OAAQC,EAAeN,GAkBrB,SAAUU,EACdC,EACAC,EACAC,EACAC,EACAb,QAAA,IAAAA,IAAAA,EAAY,GAEN,IAAAC,EAAoBC,EAAWS,EAAUR,MAAOQ,EAAUP,OAAQJ,GAAhEG,UAAOC,WAEf,MAAO,CACLU,EAAGC,EAAsBL,EAASI,EAAGX,EAAOS,EAAST,MAAOU,GAC5DG,EAAGD,EAAsBL,EAASM,EAAGZ,EAAQQ,EAASR,OAAQS,IAIlE,SAASE,EACPL,EACAC,EACAC,EACAC,GAEA,IAAMI,EAAeN,EAAYE,EAAQ,EAAID,EAAW,EAExD,OAAOM,EAAMR,GAAWO,EAAaA,GAGvB,SAAAE,EAAyBC,EAAeC,GACtD,OAAOf,KAAKgB,KAAKhB,KAAKiB,IAAIH,EAAOJ,EAAIK,EAAOL,EAAG,GAAKV,KAAKiB,IAAIH,EAAON,EAAIO,EAAOP,EAAG,IAGpE,SAAAU,EAAyBJ,EAAeC,GACtD,OAA+D,IAAvDf,KAAKmB,MAAMJ,EAAOL,EAAII,EAAOJ,EAAGK,EAAOP,EAAIM,EAAON,GAAYR,KAAKoB,GAO7D,SAAAC,EACdC,EACAjB,EACAC,EACAb,EACAc,EACAb,EACAS,QADA,IAAAT,IAAAA,EAAY,QACZ,IAAAS,IAAAA,GAAuB,GAIvB,IAAMoB,EAAcpB,EAAmBqB,EAAYC,EAE7CC,EAAgB9B,EAAWS,EAAUR,MAAOQ,EAAUP,OAAQJ,GAC9DiC,EAAuB/B,EAAWS,EAAUuB,aAAcvB,EAAUwB,cAAenC,GAInFoC,EAAyB,CAC7BtB,EAAGe,EACD,MACGG,EAAc7B,MAAQS,EAAST,MAAQU,GAAQ,EAAIe,EAAKd,EAAID,GAAQmB,EAAc7B,MACnF,KAEJa,EAAGa,EACD,MACGG,EAAc5B,OAASQ,EAASR,OAASS,GAAQ,EAAIe,EAAKZ,EAAIH,GAC/DmB,EAAc5B,OACd,KAEJD,MAAO0B,EAAY,IAAOjB,EAAST,MAAQ6B,EAAc7B,MAAS,IAAOU,GACzET,OAAQyB,EAAY,IAAOjB,EAASR,OAAS4B,EAAc5B,OAAU,IAAOS,IAIxEwB,EAAgB/B,KAAKgC,MACzBT,EACEI,EAAqB9B,MACpBiC,EAAuBjC,MAAQ8B,EAAqB9B,MAAS,MAG5DoC,EAAiBjC,KAAKgC,MAC1BT,EACEI,EAAqB7B,OACpBgC,EAAuBhC,OAAS6B,EAAqB7B,OAAU,MAS9DoC,EANqBP,EAAqB9B,OAAS8B,EAAqB7B,OAASL,EAOnF,CACEI,MAAOG,KAAKgC,MAAMC,EAAiBxC,GACnCK,OAAQmC,GAEV,CACEpC,MAAOkC,EACPjC,OAAQE,KAAKgC,MAAMD,EAAgBtC,IAmBzC,MAAO,CAAEqC,uBAAsBA,EAAEK,kBAhBVC,EAAAA,SAAAA,EAAAA,SAAA,GAClBF,GAAU,CACb1B,EAAGR,KAAKgC,MACNT,EACEI,EAAqB9B,MAAQqC,EAAWrC,MACvCiC,EAAuBtB,EAAImB,EAAqB9B,MAAS,MAG9Da,EAAGV,KAAKgC,MACNT,EACEI,EAAqB7B,OAASoC,EAAWpC,OACxCgC,EAAuBpB,EAAIiB,EAAqB7B,OAAU,SAWnE,SAAS0B,EAAUa,EAAaC,GAC9B,OAAOtC,KAAKC,IAAIoC,EAAKrC,KAAKqC,IAAI,EAAGC,IAGnC,SAASb,EAAKc,EAAcD,GAC1B,OAAOA,EAMO,SAAAE,EACdV,EACAzB,EACAX,EACAY,EACAmC,EACAC,GAEA,IAAMhB,EAAgB9B,EAAWS,EAAUR,MAAOQ,EAAUP,OAAQJ,GAG9Da,EAAOK,EACVN,EAAST,MAAQ6B,EAAc7B,OAAU,IAAMiC,EAAuBjC,OACvE4C,EACAC,GAcF,MAAO,CAAEpB,KAXI,CACXd,EACGD,EAAOmB,EAAc7B,MAAS,EAC/BS,EAAST,MAAQ,EACjB6B,EAAc7B,MAAQU,GAAQuB,EAAuBtB,EAAI,KAC3DE,EACGH,EAAOmB,EAAc5B,OAAU,EAChCQ,EAASR,OAAS,EAClB4B,EAAc5B,OAASS,GAAQuB,EAAuBpB,EAAI,MAG/CH,KAAIA,GAqBL,SAAAoC,EACdR,EACA9B,EACAX,EACAY,EACAmC,EACAC,QAHA,IAAAhD,IAAAA,EAAY,GAKZ,IAAMiC,EAAuB/B,EAAWS,EAAUuB,aAAcvB,EAAUwB,cAAenC,GAEnFa,EAAOK,EAzBf,SACEuB,EACA9B,EACAC,GAEA,IAAMsC,EAvLF,SAAuBvC,GAE3B,OAAOA,EAAUR,MAAQQ,EAAUP,OAC/BO,EAAUR,MAAQQ,EAAUuB,aAC5BvB,EAAUP,OAASO,EAAUwB,cAmLfgB,CAAaxC,GAE/B,OAAOC,EAASR,OAASQ,EAAST,MAC9BS,EAASR,QAAUqC,EAAkBrC,OAAS8C,GAC9CtC,EAAST,OAASsC,EAAkBtC,MAAQ+C,GAiB9CE,CAA6BX,EAAmB9B,EAAWC,GAC3DmC,EACAC,GAGIK,EACJzC,EAASR,OAASQ,EAAST,MACvBS,EAASR,OAASqC,EAAkBrC,OACpCQ,EAAST,MAAQsC,EAAkBtC,MASzC,MAAO,CAAEyB,KAPI,CACXd,IACImB,EAAqB9B,MAAQsC,EAAkBtC,OAAS,EAAIsC,EAAkB3B,GAAKuC,EACvFrC,IACIiB,EAAqB7B,OAASqC,EAAkBrC,QAAU,EAAIqC,EAAkBzB,GAClFqC,GAEWxC,KAAIA,GAML,SAAAyC,EAAUC,EAAUC,GAClC,MAAO,CACL1C,GAAI0C,EAAE1C,EAAIyC,EAAEzC,GAAK,EACjBE,GAAIwC,EAAExC,EAAIuC,EAAEvC,GAAK,YAWLd,EAAWC,EAAeC,EAAgBJ,GACxD,IAAMyD,EAAwBzD,EAPRM,KAAKoB,GAAM,IASjC,MAAO,CACLvB,MAAOG,KAAKoD,IAAIpD,KAAKqD,IAAIF,GAAUtD,GAASG,KAAKoD,IAAIpD,KAAKsD,IAAIH,GAAUrD,GACxEA,OAAQE,KAAKoD,IAAIpD,KAAKsD,IAAIH,GAAUtD,GAASG,KAAKoD,IAAIpD,KAAKqD,IAAIF,GAAUrD,aAO7Dc,EAAM0B,EAAerC,EAAaoC,GAChD,OAAOrC,KAAKC,IAAID,KAAKqC,IAAIC,EAAOrC,GAAMoC,YAMxBkB,QAAW,IAAgEC,EAAA,GAAAC,EAAA,EAAhEA,EAAgEC,UAAAC,OAAhEF,IAAAD,EAAgEC,GAAAC,UAAAD,GACzF,OAAOD,EACJI,QAAO,SAACtB,GACP,MAAqB,iBAAVA,GAAsBA,EAAMqB,OAAS,KAMjDE,KAAK,KACLC,WC1NLC,EAAA,SAAAC,GAAA,SAAAD,IAAA,IAozBCE,EAAA,OAAAD,GAAAA,EAAAE,MAAAC,KAAAT,YAAAS,YAhyBCF,EAAAG,WAA8CC,EAAMC,YACpDL,EAAAM,SAA8CF,EAAMC,YACpDL,EAAAO,SAA8CH,EAAMC,YACpDL,EAAiBQ,kBAAU,CAAEjE,EAAG,EAAGE,EAAG,GACtCuD,EAAYS,aAA0B,KACtCT,EAAQU,SAA4B,KACpCV,EAAaW,cAAmB,KAChCX,EAAA5D,UAAuB,CAAER,MAAO,EAAGC,OAAQ,EAAG8B,aAAc,EAAGC,cAAe,GAC9EoC,EAAiBY,kBAAU,CAAErE,EAAG,EAAGE,EAAG,GACtCuD,EAAaa,cAAU,CAAEtE,EAAG,EAAGE,EAAG,GAClCuD,EAAgBc,iBAAG,EACnBd,EAAoBe,qBAAG,EACvBf,EAAUgB,YAAG,EACbhB,EAAiBiB,kBAAG,EACpBjB,EAAiBkB,kBAAG,EACpBlB,EAAcmB,eAAkB,KAChCnB,EAAeoB,gBAAkB,KACjCpB,EAAUqB,WAAkB,KAC5BrB,EAAAsB,WAAkD,oBAAbC,SAA2BA,SAAW,KAC3EvB,EAAAwB,cAAiD,oBAAXC,OAAyBA,OAAS,KACxEzB,EAAc0B,eAA0B,KAExC1B,EAAA2B,MAAe,CACbtF,SAAU,KACVuF,qBAAqB,EACrBC,oBAAgBC,GA4GlB9B,EAAA+B,mBAAqB,WACnB,QAAqC,IAA1BN,OAAOO,gBAAmChC,EAAKS,aAA1D,CAGA,IAAIwB,GAAgB,EACpBjC,EAAK0B,eAAiB,IAAID,OAAOO,gBAAe,SAACE,GAC3CD,EACFA,GAAgB,EAGlBjC,EAAKmC,kBAEPnC,EAAK0B,eAAeU,QAAQpC,EAAKS,gBAInCT,EAAiBqC,kBAAG,SAACC,GAAa,OAAAA,EAAEC,kBAEpCvC,EAAAwC,YAAc,WACPxC,EAAKsB,aACVtB,EAAKsB,WAAWmB,oBAAoB,YAAazC,EAAK0C,aACtD1C,EAAKsB,WAAWmB,oBAAoB,UAAWzC,EAAK2C,eACpD3C,EAAKsB,WAAWmB,oBAAoB,YAAazC,EAAK4C,aACtD5C,EAAKsB,WAAWmB,oBAAoB,WAAYzC,EAAK2C,eACrD3C,EAAKsB,WAAWmB,oBAAoB,gBAAiBzC,EAAK6C,iBAC1D7C,EAAKsB,WAAWmB,oBAAoB,aAAczC,EAAK8C,cACvD9C,EAAKsB,WAAWmB,oBAAoB,SAAUzC,EAAK+C,YAGrD/C,EAAAgD,iBAAmB,WACbhD,EAAKS,cAAcT,EAAKS,aAAagC,oBAAoB,QAASzC,EAAKiD,SACvEjD,EAAKqB,YACP6B,aAAalD,EAAKqB,aAItBrB,EAAAmD,YAAc,WACZ,IAAM9G,EAAW2D,EAAKmC,eAElB9F,IACF2D,EAAKoD,eACLpD,EAAKqD,eAAehH,IAGlB2D,EAAKsD,MAAMC,eACbvD,EAAKsD,MAAMC,cAAcvD,EAAK5D,YAIlC4D,EAAcqD,eAAG,SAAChH,GAChB,GAAI2D,EAAKsD,MAAME,8BAA+B,CACtC,IAAA9H,EAAiB6C,EACrByB,EAAKsD,MAAME,8BACXxD,EAAK5D,UACL4D,EAAKsD,MAAM7H,SACXY,EACA2D,EAAKsD,MAAM9E,QACXwB,EAAKsD,MAAM7E,SANLpB,SAAMf,SASd0D,EAAKsD,MAAMG,aAAapG,GACxB2C,EAAKsD,MAAMI,cAAgB1D,EAAKsD,MAAMI,aAAapH,QAC9C,GAAI0D,EAAKsD,MAAMK,yBAA0B,CACxC,IAAAC,EAAiBlF,EACrBsB,EAAKsD,MAAMK,yBACX3D,EAAK5D,UACL4D,EAAKsD,MAAM7H,SACXY,EACA2D,EAAKsD,MAAM9E,QACXwB,EAAKsD,MAAM7E,SANLpB,SAAMf,SASd0D,EAAKsD,MAAMG,aAAapG,GACxB2C,EAAKsD,MAAMI,cAAgB1D,EAAKsD,MAAMI,aAAapH,KAiCvD0D,EAAAmC,aAAe,2BACP0B,EAAW7D,EAAKM,SAASwD,SAAW9D,EAAKO,SAASuD,QAExD,GAAID,GAAY7D,EAAKS,aAAc,CACjCT,EAAKW,cAAgBX,EAAKS,aAAasD,wBACvC/D,EAAKgE,wBACL,IAAMC,EAAkBjE,EAAKW,cAAc/E,MAAQoE,EAAKW,cAAc9E,OAChE8B,GACmB,QAAvBjC,EAAAsE,EAAKM,SAASwD,eAAS,IAAApI,OAAA,EAAAA,EAAAiC,wBAAgBiG,EAAA5D,EAAKO,SAASuD,8BAASI,aAAc,EACxEtG,GACmB,QAAvBuG,EAAAnE,EAAKM,SAASwD,eAAS,IAAAK,OAAA,EAAAA,EAAAvG,yBAAiBwG,EAAApE,EAAKO,SAASuD,8BAASO,cAAe,EAG1EC,EAAc3G,EAAeC,EAQ/B2G,SAEJ,GAXEV,EAASW,YAAc7G,GAAgBkG,EAASY,aAAe7G,EAY/D,OAAQoC,EAAK2B,MAAME,gBACjB,QACA,IAAK,UACH0C,EACEN,EAAkBK,EACd,CACE1I,MAAOoE,EAAKW,cAAc9E,OAASyI,EACnCzI,OAAQmE,EAAKW,cAAc9E,QAE7B,CACED,MAAOoE,EAAKW,cAAc/E,MAC1BC,OAAQmE,EAAKW,cAAc/E,MAAQ0I,GAE3C,MACF,IAAK,mBACHC,EAAoB,CAClB3I,MAAOoE,EAAKW,cAAc/E,MAC1BC,OAAQmE,EAAKW,cAAc/E,MAAQ0I,GAErC,MACF,IAAK,iBACHC,EAAoB,CAClB3I,MAAOoE,EAAKW,cAAc9E,OAASyI,EACnCzI,OAAQmE,EAAKW,cAAc9E,aAKjC0I,EAAoB,CAClB3I,MAAOiI,EAASW,YAChB3I,OAAQgI,EAASY,cAIrBzE,EAAK5D,UAAS+B,EAAAA,SAAAA,EAAAA,SAAA,GACToG,GAAiB,CACpB5G,aAAYA,EACZC,cAAaA,IAIXoC,EAAKsD,MAAMoB,cACb1E,EAAKsD,MAAMoB,aAAa1E,EAAK5D,WAG/B,IAAMC,EAAW2D,EAAKsD,MAAMjH,SACxB2D,EAAKsD,MAAMjH,SACXlB,EACE6E,EAAK5D,UAAUR,MACfoE,EAAK5D,UAAUP,OACfmE,EAAKW,cAAc/E,MACnBoE,EAAKW,cAAc9E,OACnBmE,EAAKsD,MAAM9H,OACXwE,EAAKsD,MAAM7H,UAejB,OAXqB,QAAnBkJ,EAAA3E,EAAK2B,MAAMtF,gBAAQ,IAAAsI,OAAA,EAAAA,EAAE9I,UAAWQ,EAASR,SACpB,QAArB+I,EAAA5E,EAAK2B,MAAMtF,gBAAU,IAAAuI,OAAA,EAAAA,EAAAhJ,SAAUS,EAAST,OAExCoE,EAAKsD,MAAMuB,kBAAoB7E,EAAKsD,MAAMuB,iBAAiBxI,GAE7D2D,EAAK8E,SAAS,CAAEzI,SAAQA,GAAI2D,EAAK+E,uBAE7B/E,EAAKsD,MAAM0B,aACbhF,EAAKsD,MAAM0B,YAAY3I,GAGlBA,IAIX2D,EAAAgE,sBAAwB,WACtB,GAAIhE,EAAKS,aAAc,CACrB,IAAMwE,EAASjF,EAAKS,aAAasD,wBACjC/D,EAAKQ,kBAAoB,CAAEjE,EAAG0I,EAAOC,KAAMzI,EAAGwI,EAAOE,OAczDnF,EAAWoF,YAAG,SAAC9C,GACRtC,EAAKsB,aACVgB,EAAEC,iBACFvC,EAAKsB,WAAW+D,iBAAiB,YAAarF,EAAK0C,aACnD1C,EAAKsB,WAAW+D,iBAAiB,UAAWrF,EAAK2C,eACjD3C,EAAKgE,wBACLhE,EAAKsF,YAAYxF,EAAQyF,cAAcjD,MAGzCtC,EAAA0C,YAAc,SAACJ,GAAkB,OAAAtC,EAAKwF,OAAO1F,EAAQyF,cAAcjD,KAEnEtC,EAAQ+C,SAAG,SAACT,GACLtC,EAAKsB,aACVgB,EAAEC,iBACFvC,EAAKgE,0BAGPhE,EAAYyF,aAAG,SAACnD,GACTtC,EAAKsB,aACVtB,EAAKgB,YAAa,EACdhB,EAAKsD,MAAMoC,iBAAmB1F,EAAKsD,MAAMoC,eAAepD,KAI5DtC,EAAKsB,WAAW+D,iBAAiB,YAAarF,EAAK4C,YAAa,CAAE+C,SAAS,IAC3E3F,EAAKsB,WAAW+D,iBAAiB,WAAYrF,EAAK2C,eAElD3C,EAAKgE,wBAEoB,IAArB1B,EAAEsD,QAAQlG,OACZM,EAAK6F,aAAavD,GACY,IAArBA,EAAEsD,QAAQlG,QACnBM,EAAKsF,YAAYxF,EAAQgG,cAAcxD,EAAEsD,QAAQ,QAIrD5F,EAAW4C,YAAG,SAACN,GAEbA,EAAEC,iBACuB,IAArBD,EAAEsD,QAAQlG,OACZM,EAAK+F,YAAYzD,GACa,IAArBA,EAAEsD,QAAQlG,QACnBM,EAAKwF,OAAO1F,EAAQgG,cAAcxD,EAAEsD,QAAQ,MAIhD5F,EAAcgG,eAAG,SAAC1D,GACXtC,EAAKsB,aACVgB,EAAEC,iBACFvC,EAAKsB,WAAW+D,iBAAiB,gBAAiBrF,EAAK6C,iBACvD7C,EAAKsB,WAAW+D,iBAAiB,aAAcrF,EAAK8C,cACpD9C,EAAKc,iBAAmBd,EAAKsD,MAAMhH,KACnC0D,EAAKe,qBAAuBf,EAAKsD,MAAM7H,WAGzCuE,EAAe6C,gBAAG,SAACP,GAEjB,GADAA,EAAEC,kBACEvC,EAAKgB,WAAT,CAKA,IAAMiF,EAAQnG,EAAQyF,cAAcjD,GAC9B4D,EAAUlG,EAAKc,iBAAmB,EAAIwB,EAAE6D,MAE9C,GADAnG,EAAKoG,WAAWF,EAASD,EAAO,CAAEI,sBAAsB,IACpDrG,EAAKsD,MAAMgD,iBAAkB,CAC/B,IAAMC,EAAcvG,EAAKe,qBAAuBuB,EAAE7G,SAClDuE,EAAKsD,MAAMgD,iBAAiBC,MAIhCvG,EAAY8C,aAAG,SAACR,GACdtC,EAAKwC,eAGPxC,EAAWsF,YAAG,SAAC5J,WAAEa,EAACb,EAAAa,EAAEE,EAACf,EAAAe,EACnBuD,EAAKY,kBAAoB,CAAErE,IAAGE,EAACA,GAC/BuD,EAAKa,cAAqB1C,WAAA,GAAA6B,EAAKsD,MAAMjG,cACrC8G,KAAAnE,EAAKsD,OAAMkD,4CAGbxG,EAAMwF,OAAG,SAAC9J,OAAEa,EAACb,EAAAa,EAAEE,EAACf,EAAAe,EACTuD,EAAKwB,gBACNxB,EAAKmB,gBAAgBnB,EAAKwB,cAAciF,qBAAqBzG,EAAKmB,gBAEtEnB,EAAKmB,eAAiBnB,EAAKwB,cAAckF,uBAAsB,WAC7D,GAAK1G,EAAK2B,MAAMtF,eACNyF,IAANvF,QAAyBuF,IAANrF,EAAvB,CACA,IAAMkK,EAAUpK,EAAIyD,EAAKY,kBAAkBrE,EACrCqK,EAAUnK,EAAIuD,EAAKY,kBAAkBnE,EACrCoK,EAAoB,CACxBtK,EAAGyD,EAAKa,cAActE,EAAIoK,EAC1BlK,EAAGuD,EAAKa,cAAcpE,EAAImK,GAGtBE,EAAc9G,EAAKsD,MAAMpH,iBAC3BA,EACE2K,EACA7G,EAAK5D,UACL4D,EAAK2B,MAAMtF,SACX2D,EAAKsD,MAAMhH,KACX0D,EAAKsD,MAAM7H,UAEboL,EACJ7G,EAAKsD,MAAMG,aAAaqD,SAI5B9G,EAAA2C,cAAgB,mBACd3C,EAAKgB,YAAa,EAClBhB,EAAKwC,cACLxC,EAAKoD,uBACLQ,KAAA5D,EAAKsD,OAAMyD,0CAgCb/G,EAAOiD,QAAG,SAACX,GACT,GAAKtC,EAAKwB,iBACNxB,EAAKsD,MAAM0D,gBAAmBhH,EAAKsD,MAAM0D,eAAe1E,IAA5D,CAIAA,EAAEC,iBACF,IAAM0D,EAAQnG,EAAQyF,cAAcjD,GAC5B2E,EAAWC,EAAAA,QAAe5E,UAC5B4D,EAAUlG,EAAKsD,MAAMhH,KAAQ2K,EAASjH,EAAKsD,MAAM6D,UAAa,IACpEnH,EAAKoG,WAAWF,EAASD,EAAO,CAAEI,sBAAsB,IAEnDrG,EAAK2B,MAAMC,qBACd5B,EAAK8E,SAAS,CAAElD,qBAAqB,IAAQ,WAAM,IAAAlG,EAAAkI,EAAA,OAAiC,WAAjClI,EAAAsE,EAAKsD,OAAMkD,0BAAsB,IAAA5C,OAAA,EAAAA,EAAAwD,KAAA1L,MAGlFsE,EAAKqB,YACP6B,aAAalD,EAAKqB,YAEpBrB,EAAKqB,WAAarB,EAAKwB,cAAc6F,YACnC,WAAM,OAAArH,EAAK8E,SAAS,CAAElD,qBAAqB,IAAS,mBAAM,OAA+B,QAA/BgC,KAAA5D,EAAKsD,OAAMyD,wBAAoB,IAAAnD,OAAA,EAAAA,EAAAwD,KAAA1L,QACzF,OAIJsE,EAAAsH,oBAAsB,SAAC5L,EAAiB6L,OAAfhL,EAACb,EAAAa,EAAEE,EAACf,EAAAe,EAC3B,IAAKuD,EAAKW,cACR,MAAM,IAAI6G,MAAM,8BAElB,MAAO,CACLjL,EAAGyD,EAAKW,cAAc/E,MAAQ,GAAKW,EAAIgL,EAAiBhL,GACxDE,EAAGuD,EAAKW,cAAc9E,OAAS,GAAKY,EAAI8K,EAAiB9K,KAI7DuD,EAAeyH,gBAAG,SAAC/L,OAAEa,EAACb,EAAAa,EAAEE,EAACf,EAAAe,EACjBmH,EAAiB5D,EAAKsD,MAApBjG,EAAIuG,EAAAvG,KAAEf,EAAIsH,EAAAtH,KAClB,MAAO,CACLC,GAAIA,EAAIc,EAAKd,GAAKD,EAClBG,GAAIA,EAAIY,EAAKZ,GAAKH,IAItB0D,EAAAoG,WAAa,SAAC9J,EAAc2J,EAAcvK,OAAEyI,QAAgC,IAAAzI,EAAA,MAAhC2K,qBAAAA,OAAoB,IAAAlC,GAAOA,EACrE,GAAKnE,EAAK2B,MAAMtF,UAAa2D,EAAKsD,MAAMI,aAAxC,CAEA,IAAMwC,EAAUvJ,EAAML,EAAM0D,EAAKsD,MAAM9E,QAASwB,EAAKsD,MAAM7E,SAE3D,GAAI4H,EAAsB,CACxB,IAAMqB,EAAY1H,EAAKsH,oBAAoBrB,EAAOjG,EAAKQ,mBACjDmH,EAAa3H,EAAKyH,gBAAgBC,GAClCb,EAAoB,CACxBtK,EAAGoL,EAAWpL,EAAI2J,EAAUwB,EAAUnL,EACtCE,EAAGkL,EAAWlL,EAAIyJ,EAAUwB,EAAUjL,GAGlCqK,EAAc9G,EAAKsD,MAAMpH,iBAC3BA,EACE2K,EACA7G,EAAK5D,UACL4D,EAAK2B,MAAMtF,SACX6J,EACAlG,EAAKsD,MAAM7H,UAEboL,EAEJ7G,EAAKsD,MAAMG,aAAaqD,GAE1B9G,EAAKsD,MAAMI,aAAawC,KAG1BlG,EAAA4H,YAAc,WACZ,OAAK5H,EAAK2B,MAAMtF,SAcTe,EAToB4C,EAAKsD,MAAMpH,iBAClCA,EACE8D,EAAKsD,MAAMjG,KACX2C,EAAK5D,UACL4D,EAAK2B,MAAMtF,SACX2D,EAAKsD,MAAMhH,KACX0D,EAAKsD,MAAM7H,UAEbuE,EAAKsD,MAAMjG,KAGb2C,EAAK5D,UACL4D,EAAK2B,MAAMtF,SACX2D,EAAK6H,YACL7H,EAAKsD,MAAMhH,KACX0D,EAAKsD,MAAM7H,SACXuE,EAAKsD,MAAMpH,kBApBJ,MAwBX8D,EAAAoD,aAAe,WACb,IAAM0E,EAAW9H,EAAK4H,cACtB,GAAKE,EAAL,CAEQ,IAAAjK,EAA8CiK,EAAQjK,uBAA9BK,EAAsB4J,EAAQ5J,kBAC1D8B,EAAKsD,MAAMyE,gBACb/H,EAAKsD,MAAMyE,eAAelK,EAAwBK,GAGhD8B,EAAKsD,MAAM0E,kBACbhI,EAAKsD,MAAM0E,iBAAiBnK,EAAwBK,KAIxD8B,EAAAiI,mBAAqB,WACnB,IAAMH,EAAW9H,EAAK4H,cACtB,GAAKE,EAAL,CAEQ,IAAAjK,EAA8CiK,EAAQjK,uBAA9BK,EAAsB4J,EAAQ5J,kBAC1D8B,EAAKsD,MAAM0E,kBACbhI,EAAKsD,MAAM0E,iBAAiBnK,EAAwBK,KAIxD8B,EAAA+E,sBAAwB,WACtB,GAAK/E,EAAK2B,MAAMtF,SAAhB,CAEA,IAAMyK,EAAc9G,EAAKsD,MAAMpH,iBAC3BA,EACE8D,EAAKsD,MAAMjG,KACX2C,EAAK5D,UACL4D,EAAK2B,MAAMtF,SACX2D,EAAKsD,MAAMhH,KACX0D,EAAKsD,MAAM7H,UAEbuE,EAAKsD,MAAMjG,KAEf2C,EAAKsD,MAAMG,aAAaqD,GACxB9G,EAAKoD,iBAGPpD,EAASkI,UAAG,SAACC,WACLhE,EAAuDnE,EAAKsD,MAA1DjG,SAAMoG,iBAAc2E,iBAAc9L,SAAMb,aAC5C4M,EAAOD,EAEX,GAAKpI,EAAK2B,MAAMtF,SAAhB,CAGI8L,EAAMG,WACRD,GAAQ,IAGV,IAAIE,EAAOpK,EAAAA,SAAA,GAAQd,GAEnB,OAAQ8K,EAAMK,KACZ,IAAK,UACHD,EAAQ9L,GAAK4L,EACbF,EAAM5F,iBACN,MACF,IAAK,YACHgG,EAAQ9L,GAAK4L,EACbF,EAAM5F,iBACN,MACF,IAAK,YACHgG,EAAQhM,GAAK8L,EACbF,EAAM5F,iBACN,MACF,IAAK,aACHgG,EAAQhM,GAAK8L,EACbF,EAAM5F,iBACN,MACF,QACE,OAGAvC,EAAKsD,MAAMpH,mBACbqM,EAAUrM,EAAiBqM,EAASvI,EAAK5D,UAAW4D,EAAK2B,MAAMtF,SAAUC,EAAMb,IAG5E0M,EAAMM,gBACT7E,KAAA5D,EAAKsD,OAAMkD,2CAGb/C,EAAa8E,KAGfvI,EAAO0I,QAAG,SAACP,WACT,OAAQA,EAAMK,KACZ,IAAK,UACL,IAAK,YACL,IAAK,YACL,IAAK,aACHL,EAAM5F,iBACN,MACF,QACE,OAEJvC,EAAKoD,uBACLQ,KAAA5D,EAAKsD,OAAMyD,4CA6Gf,OApzBsB4B,YAAoC7I,EAAAC,GAgDxDD,EAAA8I,UAAAC,kBAAA,WACO3I,KAAKoB,YAAepB,KAAKsB,gBAC1BtB,KAAKO,eACHP,KAAKO,aAAaqI,gBACpB5I,KAAKoB,WAAapB,KAAKO,aAAaqI,eAElC5I,KAAKoB,WAAWyH,cAClB7I,KAAKsB,cAAgBtB,KAAKoB,WAAWyH,aAGvC7I,KAAK6B,0BAEgC,IAA1BN,OAAOO,gBAChB9B,KAAKsB,cAAc6D,iBAAiB,SAAUnF,KAAKiC,cAErDjC,KAAKoD,MAAM0F,gBACT9I,KAAKO,aAAa4E,iBAAiB,QAASnF,KAAK+C,QAAS,CAAE0C,SAAS,IACvEzF,KAAKO,aAAa4E,iBAAiB,eAAgBnF,KAAK8F,iBAG1D9F,KAAKoB,WAAW+D,iBAAiB,SAAUnF,KAAK6C,UAE3C7C,KAAKoD,MAAM2F,kCACd/I,KAAKQ,SAAWR,KAAKoB,WAAW4H,cAAc,SAC9ChJ,KAAKQ,SAASyI,aAAa,OAAQ,YAC/BjJ,KAAKoD,MAAM8F,OACblJ,KAAKQ,SAASyI,aAAa,QAASjJ,KAAKoD,MAAM8F,OAEjDlJ,KAAKQ,SAAS2I,oiDACdnJ,KAAKoB,WAAWgI,KAAKC,YAAYrJ,KAAKQ,WAIpCR,KAAKI,SAASwD,SAAW5D,KAAKI,SAASwD,QAAQ0F,UACjDtJ,KAAKiD,cAIHjD,KAAKoD,MAAMmG,aACbvJ,KAAKoD,MAAMmG,YAAYvJ,KAAKI,UAG1BJ,KAAKoD,MAAMoG,aACbxJ,KAAKoD,MAAMoG,YAAYxJ,KAAKK,UAG1BL,KAAKoD,MAAMqG,eACbzJ,KAAKoD,MAAMqG,cAAczJ,KAAKC,cAIlCL,EAAA8I,UAAAgB,qBAAA,mBACO1J,KAAKoB,YAAepB,KAAKsB,qBACO,IAA1BC,OAAOO,gBAChB9B,KAAKsB,cAAciB,oBAAoB,SAAUvC,KAAKiC,cAEnC,QAArBzG,EAAAwE,KAAKwB,sBAAgB,IAAAhG,GAAAA,EAAAmO,aACjB3J,KAAKO,cACPP,KAAKO,aAAagC,oBAAoB,eAAgBvC,KAAKmC,mBAGzDnC,KAAKQ,WACiB,QAAxBkD,EAAA1D,KAAKQ,SAASoJ,kBAAU,IAAAlG,GAAAA,EAAEmG,YAAY7J,KAAKQ,WAG7CR,KAAKsC,cACLtC,KAAKoD,MAAM0F,gBAAkB9I,KAAK8C,qBAGpClD,EAAkB8I,UAAAoB,mBAAlB,SAAmBC,yBACbA,EAAUxO,WAAayE,KAAKoD,MAAM7H,UACpCyE,KAAKiC,eACLjC,KAAK6E,yBACIkF,EAAUzO,SAAW0E,KAAKoD,MAAM9H,QAEhCyO,EAAUC,YAAchK,KAAKoD,MAAM4G,UAD5ChK,KAAKiC,eAGI8H,EAAU3N,OAAS4D,KAAKoD,MAAMhH,KACvC4D,KAAK6E,iCAELrJ,EAAAuO,EAAU5N,+BAAUR,WAA8B,QAAnB+H,EAAA1D,KAAKoD,MAAMjH,gBAAQ,IAAAuH,OAAA,EAAAA,EAAE/H,UAClC,UAAlBoO,EAAU5N,gBAAQ,IAAA8H,OAAA,EAAAA,EAAEvI,UAA+B,QAArBwI,EAAAlE,KAAKoD,MAAMjH,gBAAU,IAAA+H,OAAA,EAAAA,EAAAxI,OAEnDsE,KAAKiC,wBAELwC,EAAAsF,EAAU5M,2BAAMd,MAAqB,QAAfqI,EAAA1E,KAAKoD,MAAMjG,YAAI,IAAAuH,OAAA,EAAAA,EAAErI,KACzB,UAAd0N,EAAU5M,YAAI,IAAA8M,OAAA,EAAAA,EAAE1N,MAAuB,QAAjB2N,EAAAlK,KAAKoD,MAAMjG,YAAM,IAAA+M,OAAA,EAAAA,EAAA3N,IAEvCyD,KAAK+H,qBAEHgC,EAAUjB,iBAAmB9I,KAAKoD,MAAM0F,gBAAkB9I,KAAKO,eACjEP,KAAKoD,MAAM0F,eACP9I,KAAKO,aAAa4E,iBAAiB,QAASnF,KAAK+C,QAAS,CAAE0C,SAAS,IACrEzF,KAAK8C,oBAEPiH,EAAUI,QAAUnK,KAAKoD,MAAM+G,QACV,QAAvBC,EAAApK,KAAKK,SAASuD,eAAS,IAAAwG,GAAAA,EAAAC,QAGzB,IAAML,EAAYhK,KAAKsK,eACnBN,IAAchK,KAAKyB,MAAME,gBAC3B3B,KAAK4E,SAAS,CAAEjD,eAAgBqI,GAAahK,KAAKiC,eAiFtDrC,EAAA8I,UAAAf,UAAA,WACQ,IAAAnM,EAAuBwE,KAAKoD,MAA1BjH,EAAQX,EAAAW,SAAEb,EAAME,EAAAF,OACxB,OAAIa,EACKA,EAAST,MAAQS,EAASR,OAE5BL,GAGTsE,EAAA8I,UAAA4B,aAAA,uBACE,GAA6B,UAAzBtK,KAAKoD,MAAM4G,UAAuB,CAGpC,IAFiBhK,KAAKI,SAASwD,SAAW5D,KAAKK,SAASuD,UAExC5D,KAAKO,aAAc,CACjCP,KAAKS,cAAgBT,KAAKO,aAAasD,wBACvC,IAAME,EAAkB/D,KAAKS,cAAc/E,MAAQsE,KAAKS,cAAc9E,OAOtE,QALyB,QAAvBH,EAAAwE,KAAKI,SAASwD,eAAS,IAAApI,OAAA,EAAAA,EAAAiC,wBAAgBiG,EAAA1D,KAAKK,SAASuD,8BAASI,aAAc,KAErD,QAAvBC,EAAAjE,KAAKI,SAASwD,eAAS,IAAAK,OAAA,EAAAA,EAAAvG,yBAAiBwG,EAAAlE,KAAKK,SAASuD,8BAASO,cAAe,GAG3DJ,EAAkB,mBAAqB,iBAE9D,MAAO,mBAGT,OAAO/D,KAAKoD,MAAM4G,WAuOpBpK,EAAY8I,UAAA/C,aAAZ,SAAavD,GACX,IAAMzF,EAASiD,EAAQgG,cAAcxD,EAAEsD,QAAQ,IACzC9I,EAASgD,EAAQgG,cAAcxD,EAAEsD,QAAQ,IAC/C1F,KAAKe,kBAAoBrE,EAAyBC,EAAQC,GAC1DoD,KAAKgB,kBAAoBjE,EAAyBJ,EAAQC,GAC1DoD,KAAKoF,YAAYvG,EAAUlC,EAAQC,KAGrCgD,EAAW8I,UAAA7C,YAAX,SAAYzD,GAAZ,IAmBCtC,EAAAE,KAlBC,GAAKA,KAAKoB,YAAepB,KAAKsB,cAA9B,CACA,IAAM3E,EAASiD,EAAQgG,cAAcxD,EAAEsD,QAAQ,IACzC9I,EAASgD,EAAQgG,cAAcxD,EAAEsD,QAAQ,IACzC6E,EAAS1L,EAAUlC,EAAQC,GACjCoD,KAAKsF,OAAOiF,GAERvK,KAAKkB,iBAAiBlB,KAAKsB,cAAciF,qBAAqBvG,KAAKkB,iBACvElB,KAAKkB,gBAAkBlB,KAAKsB,cAAckF,uBAAsB,WAC9D,IAAMgE,EAAW9N,EAAyBC,EAAQC,GAC5CoJ,EAAUlG,EAAKsD,MAAMhH,MAAQoO,EAAW1K,EAAKiB,mBACnDjB,EAAKoG,WAAWF,EAASuE,EAAQ,CAAEpE,sBAAsB,IACzDrG,EAAKiB,kBAAoByJ,EAEzB,IAAMjP,EAAWwB,EAAyBJ,EAAQC,GAC5CyJ,EAAcvG,EAAKsD,MAAM7H,UAAYA,EAAWuE,EAAKkB,mBAC3DlB,EAAKsD,MAAMgD,kBAAoBtG,EAAKsD,MAAMgD,iBAAiBC,GAC3DvG,EAAKkB,kBAAoBzF,OA0M7BqE,EAAA8I,UAAA+B,OAAA,WAAA,MAyGC3K,EAAAE,KAxGO0D,EAcF1D,KAAKoD,MAbPsH,EAAKhH,EAAAgH,MACLP,EAAKzG,EAAAyG,MACLQ,eACAC,EAAYlH,EAAAkH,aACZC,EAASnH,EAAAmH,UACT5G,EAAAP,EAAAvG,KAAQd,EAAC4H,EAAA5H,EAAEE,EAAC0H,EAAA1H,EACZhB,aACAa,EAAIsH,EAAAtH,KACJ0O,EAASpH,EAAAoH,UACTC,EAAQrH,EAAAqH,SACRC,EAAmBtH,EAAAsH,oBACnB9G,EAAAR,EAAAuH,MAASC,mBAAgBC,EAAajH,EAAAiH,cAAEC,EAAUlH,EAAAkH,WAClD3G,EAAAf,EAAA2H,QAAWC,EAAkB7G,EAAA6G,mBAAEC,EAAiB9G,EAAA8G,kBAAEC,mBAG9CxB,EAAyC,QAA7BxO,EAAAwE,KAAKyB,MAAME,sBAAkB,IAAAnG,EAAAA,EAAAwE,KAAKsK,eAEpD,OACEpK,EACE8I,cAAA,MAAA,CAAA9D,YAAalF,KAAKkF,YAClBK,aAAcvF,KAAKuF,aACnBkG,IAAK,SAACC,GAAO,OAAC5L,EAAKS,aAAemL,GAAGC,cACzB,YACZV,MAAOC,EACPU,UAAWxM,EAAW,0BAA2BkM,IAEhDZ,EACCxK,kCACE2L,IAAI,GACJD,UAAWxM,EACT,sBACc,YAAd4K,GAA2B,wBACb,qBAAdA,GAAoC,iCACtB,mBAAdA,GAAkC,+BAClCwB,IAEGb,EAAmD,CACxDmB,IAAKpB,EACLe,IAAKzL,KAAKI,SACV6K,MACKhN,EAAAA,SAAAA,EAAAA,SAAA,GAAAmN,IACHP,UACEA,GAAa,aAAAkB,OAAa1P,EAAQ,QAAA0P,OAAAxP,wBAAehB,EAAQ,eAAAwQ,OAAc3P,EAAO,OAElF4P,OAAQhM,KAAKiD,eAGfkH,GACEjK,EACE8I,cAAA,QAAA/K,EAAAA,SAAA,CAAAgO,UACA,EAAAC,aACA,EAAAC,QACAC,OAAO,EACPR,UAAWxM,EACT,sBACc,YAAd4K,GAA2B,wBACb,qBAAdA,GAAoC,iCACtB,mBAAdA,GAAkC,+BAClCwB,IAEEb,EAAU,CACdc,IAAKzL,KAAKK,SACVgM,iBAAkBrM,KAAKiD,YACvBgI,MACKhN,EAAAA,SAAAA,EAAAA,SAAA,GAAAmN,GACH,CAAAP,UACEA,GAAa,oBAAaxO,EAAC,QAAA0P,OAAOxP,EAAe,eAAAwP,OAAAxQ,EAAsB,eAAAwQ,OAAA3P,EAAO,OAElFkQ,UAAU,KAERC,MAAMC,QAAQrC,GAASA,EAAQ,CAAC,CAAE2B,IAAK3B,KAAUsC,KAAI,SAACC,GAAS,OAC/DxM,EAAQ8I,cAAA,SAAA/K,WAAA,CAAAqK,IAAKoE,EAAKZ,KAASY,QAKlC1M,KAAKyB,MAAMtF,UACV+D,kCACEuL,IAAKzL,KAAKC,WACVgL,MAAKhN,EAAAA,SAAAA,EAAAA,SAAA,GACAkN,GACH,CAAAzP,MAAOsP,EACHnP,KAAKgC,MAAMmC,KAAKyB,MAAMtF,SAAST,OAC/BsE,KAAKyB,MAAMtF,SAAST,MACxBC,OAAQqP,EACJnP,KAAKgC,MAAMmC,KAAKyB,MAAMtF,SAASR,QAC/BqE,KAAKyB,MAAMtF,SAASR,SAE1BgR,SAAU,EACV3E,UAAWhI,KAAKgI,UAChBQ,QAASxI,KAAKwI,QAAOmD,cACT,UACZC,UAAWxM,EACT,yBACc,UAAd0L,GAAyB,8BACzBC,GAAY,6BACZQ,IAEEX,MA7yBPhL,EAAAgN,aAAe,CACpBxQ,KAAM,EACNb,SAAU,EACVD,OAAQ,EAAI,EACZiD,QAfa,EAgBbD,QAjBa,EAkBbwM,UAAW,OACXd,UAAW,UACXe,UAAU,EACVE,MAAO,GACPI,QAAS,GACTV,WAAY,GACZC,aAAc,GACd3D,UAAW,EACXjL,kBAAkB,EAClB8M,gBAAgB,EAChBZ,aA1BkB,GAmXbtI,EAAAyF,cAAgB,SAACjD,GAAoD,MAAC,CAC3E/F,EAAGwQ,OAAOzK,EAAE0K,SACZvQ,EAAGsQ,OAAOzK,EAAE2K,WAGPnN,EAAAgG,cAAgB,SAACoH,GAA+B,MAAC,CACtD3Q,EAAGwQ,OAAOG,EAAMF,SAChBvQ,EAAGsQ,OAAOG,EAAMD,WAmcnBnN,EApzBD,CAAsBM,EAAM+M"}