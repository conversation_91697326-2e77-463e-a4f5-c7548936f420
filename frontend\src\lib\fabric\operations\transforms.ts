import { Canvas } from "fabric";
import { TransformState } from "@/shared/types";

// Core transform operations
export const applyCanvasRotation = (canvas: Canvas): void => {
  const currentWidth = canvas.width!;
  const currentHeight = canvas.height!;

  const currentVpt = canvas.viewportTransform;
  const hasViewportTransform =
    currentVpt &&
    (currentVpt[0] !== 1 || currentVpt[3] !== 1 || currentVpt[4] !== 0 || currentVpt[5] !== 0);

  canvas.setDimensions({
    width: currentHeight,
    height: currentWidth,
  });

  const canvasElement = canvas.getElement();
  if (canvasElement) {
    canvasElement.width = currentHeight;
    canvasElement.height = currentWidth;
    canvasElement.style.width = `${currentHeight}px`;
    canvasElement.style.height = `${currentWidth}px`;
  }

  if (hasViewportTransform && currentVpt) {
    const [a, b, c, d, e, f] = currentVpt;
    const newVpt: [number, number, number, number, number, number] = [
      d,
      -c,
      b,
      -a,
      f * (currentHeight / currentWidth),
      -e * (currentWidth / currentHeight),
    ];
    canvas.setViewportTransform(newVpt);
  }

  if (canvas.backgroundImage) {
    const backgroundImage = canvas.backgroundImage;
    const currentAngle = backgroundImage.angle || 0;
    const newAngle = (currentAngle + 90) % 360;

    backgroundImage.rotate(newAngle);
    backgroundImage.set({
      left: currentHeight / 2,
      top: currentWidth / 2,
      originX: "center",
      originY: "center",
    });

    canvas.backgroundImage = backgroundImage;
  }

  const objects = canvas.getObjects();
  const oldCenterX = currentWidth / 2;
  const oldCenterY = currentHeight / 2;
  const newCenterX = currentHeight / 2;
  const newCenterY = currentWidth / 2;

  objects.forEach((obj) => {
    const objCenterX = obj.left! + (obj.width! * obj.scaleX!) / 2;
    const objCenterY = obj.top! + (obj.height! * obj.scaleY!) / 2;
    const relativeX = objCenterX - oldCenterX;
    const relativeY = objCenterY - oldCenterY;

    const newRelativeX = -relativeY;
    const newRelativeY = relativeX;
    const newObjCenterX = newCenterX + newRelativeX;
    const newObjCenterY = newCenterY + newRelativeY;

    obj.set({
      left: newObjCenterX - (obj.width! * obj.scaleX!) / 2,
      top: newObjCenterY - (obj.height! * obj.scaleY!) / 2,
      angle: (obj.angle || 0) + 90,
    });
  });

  canvas.requestRenderAll();
};

export const applyCanvasFlipHorizontal = (canvas: Canvas): void => {
  const currentVpt = canvas.viewportTransform;
  const hasViewportTransform =
    currentVpt &&
    (currentVpt[0] !== 1 || currentVpt[3] !== 1 || currentVpt[4] !== 0 || currentVpt[5] !== 0);

  if (canvas.backgroundImage) {
    const currentFlipX = canvas.backgroundImage.flipX || false;
    canvas.backgroundImage.set("flipX", !currentFlipX);
  }

  if (hasViewportTransform && currentVpt) {
    const [a, b, c, d, e, f] = currentVpt;
    const newVpt: [number, number, number, number, number, number] = [
      -a,
      b,
      c,
      d,
      canvas.width! * a + e,
      f,
    ];
    canvas.setViewportTransform(newVpt);
  }

  canvas.forEachObject((obj) => {
    const currentFlipX = obj.flipX || false;
    obj.set({
      flipX: !currentFlipX,
      left: canvas.width! - obj.left! - obj.width! * obj.scaleX!,
    });
  });
  canvas.requestRenderAll();
};

export const applyCanvasFlipVertical = (canvas: Canvas): void => {
  const currentVpt = canvas.viewportTransform;
  const hasViewportTransform =
    currentVpt &&
    (currentVpt[0] !== 1 || currentVpt[3] !== 1 || currentVpt[4] !== 0 || currentVpt[5] !== 0);

  if (canvas.backgroundImage) {
    const currentFlipY = canvas.backgroundImage.flipY || false;
    canvas.backgroundImage.set("flipY", !currentFlipY);
  }

  if (hasViewportTransform && currentVpt) {
    const [a, b, c, d, e, f] = currentVpt;
    const newVpt: [number, number, number, number, number, number] = [
      a,
      b,
      c,
      -d,
      e,
      canvas.height! * d + f,
    ];
    canvas.setViewportTransform(newVpt);
  }

  canvas.forEachObject((obj) => {
    const currentFlipY = obj.flipY || false;
    obj.set({
      flipY: !currentFlipY,
      top: canvas.height! - obj.top! - obj.height! * obj.scaleY!,
    });
  });
  canvas.requestRenderAll();
};

// Transform handlers
export const createRotateHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createFlipHorizontalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipHorizontal(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createFlipVerticalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipVertical(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};

export const createResetHandler = (
  transformState: TransformState,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return (canvas: Canvas) => {
    Array.from({ length: (4 - transformState.rotations) % 4 }, () => applyCanvasRotation(canvas));
    if (transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
    if (transformState.flipVertical) applyCanvasFlipVertical(canvas);
    setTransformState({ rotations: 0, flipHorizontal: false, flipVertical: false });
  };
};
