import { Canvas } from "fabric";
import { TransformState } from "@/shared/types";

const rotateCanvasObjects = (fabricCanvas: Canvas, angleInDegrees: number) => {
  const oldWidth = fabricCanvas.getWidth();
  const oldHeight = fabricCanvas.getHeight();
  const centerX = oldWidth / 2;
  const centerY = oldHeight / 2;
  const angleInRadians = (angleInDegrees * Math.PI) / 180;

  const isRightAngleRotation = angleInDegrees % 180 !== 0;

  if (isRightAngleRotation) {
    // Swap width and height for 90° or 270°
    fabricCanvas.setWidth(oldHeight);
    fabricCanvas.setHeight(oldWidth);
  }

  const newCenterX = fabricCanvas.getWidth() / 2;
  const newCenterY = fabricCanvas.getHeight() / 2;

  fabricCanvas.getObjects().forEach((obj) => {
    if ((obj as any).name === "backgroundImage") return;

    const objCenter = obj.getCenterPoint();
    const dx = objCenter.x - centerX;
    const dy = objCenter.y - centerY;

    // Rotate around old center, then shift to new center
    const rotatedX = dx * Math.cos(angleInRadians) - dy * Math.sin(angleInRadians);
    const rotatedY = dx * Math.sin(angleInRadians) + dy * Math.cos(angleInRadians);
    const newX = newCenterX + rotatedX;
    const newY = newCenterY + rotatedY;

    obj.set({
      left: newX - (obj.width! * obj.scaleX!) / 2,
      top: newY - (obj.height! * obj.scaleY!) / 2,
      angle: (obj.angle || 0) + angleInDegrees,
    });

    obj.setCoords();
  });

  if (fabricCanvas.backgroundImage) {
    const bg = fabricCanvas.backgroundImage;
    const bgAngle = (bg.angle || 0) + angleInDegrees;
    bg.rotate(bgAngle);

    // Optional: reposition background to match canvas center
    bg.set({
      left: newCenterX - (bg.width! * bg.scaleX!) / 2,
      top: newCenterY - (bg.height! * bg.scaleY!) / 2,
    });

    bg.setCoords();
  }

  fabricCanvas.requestRenderAll();
};

const flipCanvasHorizontal = (fabricCanvas: Canvas) => {
  const centerX = fabricCanvas.width! / 2;

  fabricCanvas.getObjects().forEach((obj) => {
    if ((obj as any).name === "backgroundImage") return;

    const objCenter = obj.getCenterPoint();
    const newX = centerX - (objCenter.x - centerX);

    obj.set({
      left: newX - (obj.width! * obj.scaleX!) / 2,
      flipX: !obj.flipX,
    });

    obj.setCoords();
  });

  if (fabricCanvas.backgroundImage) {
    const backgroundImage = fabricCanvas.backgroundImage;
    const currentFlipX = backgroundImage.flipX || false;
    backgroundImage.set("flipX", !currentFlipX);
  }

  fabricCanvas.requestRenderAll();
};

const flipCanvasVertical = (fabricCanvas: Canvas) => {
  const centerY = fabricCanvas.height! / 2;

  fabricCanvas.getObjects().forEach((obj) => {
    if ((obj as any).name === "backgroundImage") return;

    const objCenter = obj.getCenterPoint();
    const newY = centerY - (objCenter.y - centerY);

    obj.set({
      top: newY - (obj.height! * obj.scaleY!) / 2,
      flipY: !obj.flipY,
    });

    obj.setCoords();
  });

  if (fabricCanvas.backgroundImage) {
    const backgroundImage = fabricCanvas.backgroundImage;
    const currentFlipY = backgroundImage.flipY || false;
    backgroundImage.set("flipY", !currentFlipY);
  }

  fabricCanvas.requestRenderAll();
};

// Core transform operations
export const applyCanvasRotation = (canvas: Canvas): void => {
  rotateCanvasObjects(canvas, 90);
};

export const applyCanvasFlipHorizontal = (canvas: Canvas): void => {
  flipCanvasHorizontal(canvas);
};

export const applyCanvasFlipVertical = (canvas: Canvas): void => {
  flipCanvasVertical(canvas);
};

// Transform handlers
export const createRotateHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createFlipHorizontalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipHorizontal(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createFlipVerticalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipVertical(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};

export const createResetHandler = (
  transformState: TransformState,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return (canvas: Canvas) => {
    Array.from({ length: (4 - transformState.rotations) % 4 }, () => applyCanvasRotation(canvas));
    if (transformState.flipHorizontal) applyCanvasFlipHorizontal(canvas);
    if (transformState.flipVertical) applyCanvasFlipVertical(canvas);
    setTransformState({ rotations: 0, flipHorizontal: false, flipVertical: false });
  };
};
