export type Size = {
    width: number;
    height: number;
};
export type MediaSize = {
    width: number;
    height: number;
    naturalWidth: number;
    naturalHeight: number;
};
export type Point = {
    x: number;
    y: number;
};
export type Area = {
    width: number;
    height: number;
    x: number;
    y: number;
};
export type VideoSrc = {
    src: string;
    type?: string;
};
//# sourceMappingURL=types.d.ts.map