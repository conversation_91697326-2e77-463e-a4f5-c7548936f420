import { useEffect, useRef } from "react";
import FabricToolbar from "@/components/toolbars/FabricToolbar";
import { ImageViewerProps } from "@/shared/types";
import { useFabricViewer } from "@/hooks";
import { useResponsiveCanvas } from "@/hooks";

const ImageViewer: React.FC<ImageViewerProps> = ({ data }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const {
    canvas: fabricCanvas,
    setupCanvas,
    brightness,
    contrast,
    grayscale,
    invert,
    sharpness,
    gammaR,
    gammaG,
    gammaB,
    handleBrightnessChange,
    handleContrastChange,
    handleGrayscaleChange,
    handleInvertChange,
    handleSharpnessChange,
    handleGammaRChange,
    handleGammaGChange,
    handleGammaBChange,
    handleRotate: originalHandleRotate,
    handleFlipHorizontal: originalHandleFlipHorizontal,
    handleFlipVertical: originalHandleFlipVertical,
    handleUndo,
    handleCrop,
    handleSave,
    handleShowOriginal,
    applySavedTransforms,
    disableUndoTracking,
    enableUndoTracking,
    canUndo,
    isShowingOriginal,
    hasPerformedCrop,
    cropData,
    setCropData,
  } = useFabricViewer({
    data,
    containerRef,
  });

  const { resizeCanvas } = useResponsiveCanvas({
    fabricCanvas,
    containerRef,
    cropData,
    setCropData,
  });

  // Create wrapper functions that call the original handlers and then resize
  const handleRotate = () => {
    originalHandleRotate();
    setTimeout(resizeCanvas, 0);
  };

  const handleFlipHorizontal = () => {
    originalHandleFlipHorizontal();
    setTimeout(resizeCanvas, 0);
  };

  const handleFlipVertical = () => {
    originalHandleFlipVertical();
    setTimeout(resizeCanvas, 0);
  };

  useEffect(() => {
    async function setupViewer() {
      if (!canvasRef.current) return;
      await setupCanvas(canvasRef.current, data.viewer.imageUrl);
      resizeCanvas();
      if (!data.viewer.fabricConfigs.cropData?.isCropped) {
        applySavedTransforms();
      }
    }
    setupViewer();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data.viewer.imageUrl]);

  return (
    <div className="image-viewer" id="image-viewer-container">
      <div
        ref={containerRef}
        className={`viewer-container ${hasPerformedCrop || cropData?.isCropped ? "cropped" : ""}`}
      >
        <canvas ref={canvasRef} className="fabric-canvas" />
      </div>

      <FabricToolbar
        fabricCanvas={fabricCanvas}
        brightness={brightness}
        contrast={contrast}
        grayscale={grayscale}
        invert={invert}
        sharpness={sharpness}
        gammaR={gammaR}
        gammaG={gammaG}
        gammaB={gammaB}
        onBrightnessChange={handleBrightnessChange}
        onContrastChange={handleContrastChange}
        onGrayscaleChange={handleGrayscaleChange}
        onInvertChange={handleInvertChange}
        onSharpnessChange={handleSharpnessChange}
        onGammaRChange={handleGammaRChange}
        onGammaGChange={handleGammaGChange}
        onGammaBChange={handleGammaBChange}
        onRotate={handleRotate}
        onFlipHorizontal={handleFlipHorizontal}
        onFlipVertical={handleFlipVertical}
        onUndo={handleUndo}
        canUndo={canUndo}
        onSave={handleSave}
        onShowOriginal={handleShowOriginal}
        onCrop={handleCrop}
        disableUndoTracking={disableUndoTracking}
        enableUndoTracking={enableUndoTracking}
        isShowingOriginal={isShowingOriginal}
        hasPerformedCrop={hasPerformedCrop}
      />
    </div>
  );
};

export default ImageViewer;
