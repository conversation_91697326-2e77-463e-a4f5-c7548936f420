import { Canvas } from "fabric";
import { CropData } from "@/shared/types";

// Calculate target dimensions that fit within container while maintaining aspect ratio
export const calculateFittedCanvasDimensions = (
  contentWidth: number,
  contentHeight: number,
  containerWidth: number,
  containerHeight: number
) => {
  const aspectRatio = contentWidth / contentHeight;
  let targetWidth = containerWidth;
  let targetHeight = targetWidth / aspectRatio;

  if (targetHeight > containerHeight) {
    targetHeight = containerHeight;
    targetWidth = targetHeight * aspectRatio;
  }

  return { width: targetWidth, height: targetHeight };
};

// Check if resize is needed based on threshold
export const shouldResize = (
  currentWidth: number,
  currentHeight: number,
  targetWidth: number,
  targetHeight: number,
  threshold: number = 5
): boolean => {
  return (
    Math.abs(currentWidth - targetWidth) >= threshold ||
    Math.abs(currentHeight - targetHeight) >= threshold
  );
};



export const scaleCanvasObjects = (canvas: Canvas, imageScale: number) => {
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.set({
        scaleX: (obj.scaleX || 1) * imageScale,
        scaleY: (obj.scaleY || 1) * imageScale,
        left: (obj.left || 0) * imageScale,
        top: (obj.top || 0) * imageScale,
      });

      if ("strokeUniform" in obj) {
        obj.strokeUniform = true;
      }

      if (obj.type === "textbox" || obj.type === "text") {
        const textbox = obj as any;
        textbox.fontSize = 16;
        textbox.set({
          scaleX: 1,
          scaleY: 1,
          lockScalingX: true,
          lockScalingY: true,
          hasControls: false,
        });
      }

      obj.setCoords();
    }
  });
};

export const positionBackgroundImage = (
  bgImg: any,
  actualWidth: number,
  actualHeight: number,
  imageScale: number
) => {
  const currentAngle = bgImg.angle || 0;
  const currentFlipX = bgImg.flipX || false;
  const currentFlipY = bgImg.flipY || false;

  bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
  bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;


  bgImg.set({
    left: actualWidth / 2,
    top: actualHeight / 2,
    originX: "center",
    originY: "center",
  });

  bgImg.angle = currentAngle;
  bgImg.flipX = currentFlipX;
  bgImg.flipY = currentFlipY;
};

export const updateCropDataDimensions = (
  cropData: CropData,
  containerWidth: number,
  containerHeight: number
): CropData => {
  return {
    ...cropData,
    canvasDimensions: {
      width: containerWidth,
      height: containerHeight,
    },
  };
};
