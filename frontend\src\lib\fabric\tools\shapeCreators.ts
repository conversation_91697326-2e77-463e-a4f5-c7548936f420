import { Textbox, Rect, Line, Circle, Canvas } from "fabric";
import { ToolMode, FabricMeasurementLine } from "@/shared/types";
import { getToolConfig, transformPointer } from "./toolConfigs";

export const createInitialShape = (
  mode: ToolMode,
  pointer: { x: number; y: number },
  canvas: Canvas
) => {
  const config = getToolConfig(mode) as any;
  const transformedPointer = transformPointer(pointer, canvas);

  switch (mode) {
    case "text":
      return new Textbox("Text", {
        left: transformedPointer.x,
        top: transformedPointer.y,
        ...config,
      });
    case "rect":
      return new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        width: 1,
        height: 1,
        ...config,
      });
    case "line":
      return new Line(
        [transformedPointer.x, transformedPointer.y, transformedPointer.x, transformedPointer.y],
        {
          ...config,
        }
      );
    case "circle":
      return new Circle({
        left: transformedPointer.x,
        top: transformedPointer.y,
        radius: 1,
        ...config,
      });
    case "crop":
      return new Rect({
        left: transformedPointer.x,
        top: transformedPointer.y,
        ...config,
      });
    case "measure": {
      const line = new Line(
        [transformedPointer.x, transformedPointer.y, transformedPointer.x, transformedPointer.y],
        {
          ...config,
        }
      ) as Line & { id: string };
      // line.id = `line_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      return line;
    }
  }
};

export const updateShapeSize = (
  shape: Rect | Circle | Line | FabricMeasurementLine | Textbox,
  startPoint: { x: number; y: number },
  currentPoint: { x: number; y: number },
  mode: ToolMode,
  canvas: Canvas
) => {
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  const width = Math.abs(currentPoint.x - startPoint.x);
  const height = Math.abs(currentPoint.y - startPoint.y);
  const left = Math.min(startPoint.x, currentPoint.x);
  const top = Math.min(startPoint.y, currentPoint.y);

  switch (mode) {
    case "text":
      break;
    case "rect":
    case "crop":
      shape.set({
        left: left,
        top: top,
        width: Math.max(1, width),
        height: Math.max(1, height),
      });
      break;
    case "line":
    case "measure":
      shape.set({
        x1: startPoint.x,
        y1: startPoint.y,
        x2: currentPoint.x,
        y2: currentPoint.y,
      });
      break;
    case "circle": {
      const maxRadius = Math.min(
        (canvasWidth - left) / 2,
        (canvasHeight - top) / 2,
        width / 2,
        height / 2
      );
      const radius = Math.max(1, maxRadius);
      shape.set({
        left: left,
        top: top,
        radius: radius,
      });
      break;
    }
  }
};
