import React, { useState, useCallback, useEffect } from "react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import { Area, Point } from "react-easy-crop/types";

interface InlineCropperProps {
  imageUrl: string;
  isActive: boolean;
  onCropComplete: (cropData: {
    crop: Point;
    zoom: number;
    rotation: number;
    croppedAreaPixels: Area;
  }) => void;
  onCancel: () => void;
  initialCrop?: Point;
  initialZoom?: number;
  initialRotation?: number;
}

export const InlineCropper: React.FC<InlineCropperProps> = ({
  imageUrl,
  isActive,
  onCropComplete,
  onCancel,
  initialCrop = { x: 0, y: 0 },
  initialZoom = 1,
  initialRotation = 0,
}) => {
  const [crop, setCrop] = useState<Point>(initialCrop);
  const [zoom, setZoom] = useState(initialZoom);
  const [rotation, setRotation] = useState(initialRotation);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [hasInteracted, setHasInteracted] = useState(false);

  const onCropCompleteCallback = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const handleInteractionStart = useCallback(() => {
    setHasInteracted(true);
  }, []);

  const handleInteractionEnd = useCallback(() => {
    if (hasInteracted && croppedAreaPixels) {
      setTimeout(() => {
        onCropComplete({
          crop,
          zoom,
          rotation,
          croppedAreaPixels,
        });
      }, 100);
    }
  }, [hasInteracted, croppedAreaPixels, crop, zoom, rotation, onCropComplete]);

  useEffect(() => {
    if (!isActive) {
      setHasInteracted(false);
      setCrop(initialCrop);
      setZoom(initialZoom);
      setRotation(initialRotation);
    }
  }, [isActive, initialCrop, initialZoom, initialRotation]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isActive && e.key === "Escape") {
        onCancel();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isActive, onCancel]);

  if (!isActive) {
    return null;
  }

  return (
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
      }}
    >
      <Cropper
        image={imageUrl}
        crop={crop}
        zoom={zoom}
        rotation={rotation}
        aspect={4 / 3}
        onCropChange={setCrop}
        onCropComplete={onCropCompleteCallback}
        onZoomChange={setZoom}
        onRotationChange={setRotation}
        onInteractionStart={handleInteractionStart}
        onInteractionEnd={handleInteractionEnd}
        showGrid={true}
        zoomWithScroll={true}
        style={{
          containerStyle: {
            backgroundColor: "transparent",
          },
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "20px",
          left: "50%",
          transform: "translateX(-50%)",
          color: "white",
          fontSize: "14px",
          textAlign: "center",
        }}
      >
        Drag to position • Scroll to zoom • Press ESC to cancel
      </div>
    </div>
  );
};
