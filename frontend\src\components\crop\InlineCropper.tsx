import React, { useState, useCallback, useEffect, useRef } from "react";

interface Area {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface InlineCropperProps {
  imageUrl: string;
  isActive: boolean;
  onCropComplete: (cropData: { croppedAreaPixels: Area }) => void;
  onCancel: () => void;
}

export const InlineCropper: React.FC<InlineCropperProps> = ({
  imageUrl,
  isActive,
  onCropComplete,
  onCancel,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const [currentPos, setCurrentPos] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const imageRef = useRef<HTMLImageElement | null>(null);

  useEffect(() => {
    if (!isActive || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = new Image();
    img.onload = () => {
      const container = canvas.parentElement;
      if (!container) return;

      const containerRect = container.getBoundingClientRect();
      const imgAspect = img.width / img.height;
      const containerAspect = containerRect.width / containerRect.height;

      let displayWidth, displayHeight;
      if (imgAspect > containerAspect) {
        displayWidth = containerRect.width;
        displayHeight = containerRect.width / imgAspect;
      } else {
        displayHeight = containerRect.height;
        displayWidth = containerRect.height * imgAspect;
      }

      canvas.width = displayWidth;
      canvas.height = displayHeight;
      canvas.style.width = `${displayWidth}px`;
      canvas.style.height = `${displayHeight}px`;

      ctx.drawImage(img, 0, 0, displayWidth, displayHeight);
      setImageLoaded(true);
      imageRef.current = img;
    };
    img.src = imageUrl;
  }, [isActive, imageUrl]);

  const getMousePos = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  }, []);

  const drawRect = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    const img = imageRef.current;
    if (!canvas || !ctx || !img) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

    if (isDrawing) {
      const width = currentPos.x - startPos.x;
      const height = currentPos.y - startPos.y;

      ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.clearRect(startPos.x, startPos.y, width, height);
      ctx.drawImage(
        img,
        startPos.x,
        startPos.y,
        width,
        height,
        startPos.x,
        startPos.y,
        width,
        height
      );

      ctx.strokeStyle = "#007bff";
      ctx.lineWidth = 2;
      ctx.strokeRect(startPos.x, startPos.y, width, height);
    }
  }, [isDrawing, startPos, currentPos]);

  useEffect(() => {
    drawRect();
  }, [drawRect]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!imageLoaded) return;
      const pos = getMousePos(e);
      setStartPos(pos);
      setCurrentPos(pos);
      setIsDrawing(true);
    },
    [imageLoaded, getMousePos]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLCanvasElement>) => {
      if (!isDrawing) return;
      const pos = getMousePos(e);
      setCurrentPos(pos);
    },
    [isDrawing, getMousePos]
  );

  const handleMouseUp = useCallback(() => {
    if (!isDrawing || !canvasRef.current || !imageRef.current) return;

    const canvas = canvasRef.current;
    const img = imageRef.current;

    const width = Math.abs(currentPos.x - startPos.x);
    const height = Math.abs(currentPos.y - startPos.y);

    if (width > 10 && height > 10) {
      const scaleX = img.width / canvas.width;
      const scaleY = img.height / canvas.height;

      const croppedAreaPixels: Area = {
        x: Math.min(startPos.x, currentPos.x) * scaleX,
        y: Math.min(startPos.y, currentPos.y) * scaleY,
        width: width * scaleX,
        height: height * scaleY,
      };

      onCropComplete({ croppedAreaPixels });
    }

    setIsDrawing(false);
  }, [isDrawing, startPos, currentPos, onCropComplete]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isActive && e.key === "Escape") {
        onCancel();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isActive, onCancel]);

  if (!isActive) {
    return null;
  }

  return (
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
      }}
    >
      <canvas
        ref={canvasRef}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{
          cursor: isDrawing ? "crosshair" : "crosshair",
          border: "1px solid #ccc",
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "20px",
          left: "50%",
          transform: "translateX(-50%)",
          color: "white",
          fontSize: "14px",
          textAlign: "center",
        }}
      >
        Draw rectangle to crop • Press ESC to cancel
      </div>
    </div>
  );
};
