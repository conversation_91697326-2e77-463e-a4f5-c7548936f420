{"name": "react-easy-crop", "version": "5.5.0", "description": "A React component to crop images/videos with easy interactions", "homepage": "https://ValentinH.github.io/react-easy-crop/", "keywords": ["react", "crop", "cropper", "image crop"], "repository": {"type": "git", "url": "https://github.com/ValentinH/react-easy-crop"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "sideEffects": false, "peerDependencies": {"react": ">=16.4.0", "react-dom": ">=16.4.0"}, "dependencies": {"normalize-wheel": "^1.0.1", "tslib": "^2.0.1"}, "main": "./index.js", "umd:main": "./umd/react-easy-crop.js", "unpkg": "./umd/react-easy-crop.js", "jsdelivr": "./umd/react-easy-crop.js", "module": "./index.module.js", "jsnext:main": "./index.module.js", "react-native": "./index.module.js", "types": "./index.d.ts", "exports": {".": {"import": "./index.module.js", "require": "./index.js", "types": "./index.d.ts"}, "./react-easy-crop.css": {"import": "./react-easy-crop.css", "require": "./react-easy-crop.css"}}}