import { useState, useCallback, useRef } from "react";
import { Canvas, FabricObject } from "fabric";
import { CropData } from "@/shared/types";

interface Area {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface EasyCropState {
  cropData: CropData;
  setCropData: (data: CropData) => void;
  hasPerformedCrop: boolean;
  setHasPerformedCrop: (value: boolean) => void;
  isCropMode: boolean;
  setIsCropMode: (active: boolean) => void;
  handleCrop: () => void;
  handleCropComplete: (cropResult: { croppedAreaPixels: Area }) => void;
  handleCropCancel: () => void;
}

export const useEasyCrop = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData
): EasyCropState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);
  const [isCropMode, setIsCropMode] = useState(false);
  const originalCanvasState = useRef<{
    width: number;
    height: number;
    backgroundImage: any;
    objects: any[];
  } | null>(null);

  const handleCrop = useCallback(() => {
    if (hasPerformedCrop) {
      restoreOriginalImage();
    } else {
      setIsCropMode(true);
    }
  }, [hasPerformedCrop]);

  const handleCropComplete = useCallback(
    (cropResult: { croppedAreaPixels: Area }) => {
      if (!fabricCanvas?.current) return;

      const canvas = fabricCanvas.current;

      // Store original state before cropping
      if (!originalCanvasState.current) {
        originalCanvasState.current = {
          width: canvas.getWidth(),
          height: canvas.getHeight(),
          backgroundImage: canvas.backgroundImage,
          objects: canvas.getObjects().map((obj) => obj.toObject()),
        };
      }

      // Apply crop by creating a new canvas with cropped dimensions
      applyCropToCanvas(canvas, cropResult);

      // Update crop data
      const newCropData: CropData = {
        isCropped: true,
        normalizedCropRect: {
          left: cropResult.croppedAreaPixels.x / originalCanvasState.current.width,
          top: cropResult.croppedAreaPixels.y / originalCanvasState.current.height,
          width: cropResult.croppedAreaPixels.width / originalCanvasState.current.width,
          height: cropResult.croppedAreaPixels.height / originalCanvasState.current.height,
        },
        canvasDimensions: {
          width: cropResult.croppedAreaPixels.width,
          height: cropResult.croppedAreaPixels.height,
        },
      };

      setCropData(newCropData);
      setHasPerformedCrop(true);
      setIsCropMode(false);
    },
    [fabricCanvas]
  );

  const handleCropCancel = useCallback(() => {
    setIsCropMode(false);
  }, []);

  const restoreOriginalImage = useCallback(async () => {
    if (!fabricCanvas?.current || !originalCanvasState.current) return;

    const canvas = fabricCanvas.current;

    // Restore original canvas dimensions
    canvas.setDimensions({
      width: originalCanvasState.current.width,
      height: originalCanvasState.current.height,
    });

    // Reset viewport transform
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

    // Restore background image
    if (originalCanvasState.current.backgroundImage) {
      canvas.backgroundImage = originalCanvasState.current.backgroundImage;
    }

    // Restore objects
    canvas.clear();
    originalCanvasState.current.objects.forEach((objData) => {
      // Recreate objects from stored data
      // This is a simplified version - you might need more complex object restoration
      canvas.add(new FabricObject(objData));
    });

    // Reset state
    setCropData({ isCropped: false });
    setHasPerformedCrop(false);
    originalCanvasState.current = null;

    canvas.renderAll();
  }, [fabricCanvas]);

  const applyCropToCanvas = (
    canvas: Canvas,
    cropResult: {
      croppedAreaPixels: Area;
    }
  ) => {
    const { croppedAreaPixels } = cropResult;

    // Set canvas to cropped dimensions
    canvas.setDimensions({
      width: croppedAreaPixels.width,
      height: croppedAreaPixels.height,
    });

    // Apply viewport transform to show only the cropped area
    const scale = 1;
    const vpt: [number, number, number, number, number, number] = [
      scale,
      0,
      0,
      scale,
      -croppedAreaPixels.x,
      -croppedAreaPixels.y,
    ];

    canvas.setViewportTransform(vpt);
    canvas.renderAll();
  };

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    isCropMode,
    setIsCropMode,
    handleCrop,
    handleCropComplete,
    handleCropCancel,
  };
};
