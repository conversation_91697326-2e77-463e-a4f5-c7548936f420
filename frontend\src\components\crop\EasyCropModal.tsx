import React, { useState, useCallback } from "react";
import { Dialog, DialogB<PERSON>, Button, Intent } from "@blueprintjs/core";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import { Area, Point } from "react-easy-crop/types";
import "./EasyCropModal.css";

interface EasyCropModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  onCropComplete: (cropData: {
    crop: Point;
    zoom: number;
    rotation: number;
    croppedAreaPixels: Area;
  }) => void;
  initialCrop?: Point;
  initialZoom?: number;
  initialRotation?: number;
}

export const EasyCropModal: React.FC<EasyCropModalProps> = ({
  isOpen,
  onClose,
  imageUrl,
  onCropComplete,
  initialCrop = { x: 0, y: 0 },
  initialZoom = 1,
  initialRotation = 0,
}) => {
  const [crop, setCrop] = useState<Point>(initialCrop);
  const [zoom, setZoom] = useState(initialZoom);
  const [rotation, setRotation] = useState(initialRotation);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);

  const onCropCompleteCallback = useCallback(
    (croppedArea: Area, croppedAreaPixels: Area) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const handleApplyCrop = () => {
    if (croppedAreaPixels) {
      onCropComplete({
        crop,
        zoom,
        rotation,
        croppedAreaPixels,
      });
      onClose();
    }
  };

  const handleCancel = () => {
    setCrop(initialCrop);
    setZoom(initialZoom);
    setRotation(initialRotation);
    onClose();
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={handleCancel}
      title="Crop Image"
      className="crop-modal"
      style={{ width: "80vw", height: "80vh" }}
    >
      <DialogBody>
        <div className="crop-container">
          <div className="cropper-wrapper">
            <Cropper
              image={imageUrl}
              crop={crop}
              zoom={zoom}
              rotation={rotation}
              aspect={4 / 3}
              onCropChange={setCrop}
              onCropComplete={onCropCompleteCallback}
              onZoomChange={setZoom}
              onRotationChange={setRotation}
            />
          </div>
          <div className="crop-controls">
            <div className="control-group">
              <label>Zoom: {zoom.toFixed(2)}</label>
              <input
                type="range"
                value={zoom}
                min={1}
                max={3}
                step={0.1}
                onChange={(e) => setZoom(Number(e.target.value))}
              />
            </div>
            <div className="control-group">
              <label>Rotation: {rotation}°</label>
              <input
                type="range"
                value={rotation}
                min={0}
                max={360}
                step={1}
                onChange={(e) => setRotation(Number(e.target.value))}
              />
            </div>
          </div>
          <div className="crop-actions">
            <Button onClick={handleCancel}>Cancel</Button>
            <Button
              intent={Intent.PRIMARY}
              onClick={handleApplyCrop}
              disabled={!croppedAreaPixels}
            >
              Apply Crop
            </Button>
          </div>
        </div>
      </DialogBody>
    </Dialog>
  );
};
