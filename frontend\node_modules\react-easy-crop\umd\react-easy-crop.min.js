!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("tslib"),require("react"),require("normalize-wheel")):"function"==typeof define&&define.amd?define(["exports","tslib","react","normalize-wheel"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactEasyCrop={},e.tslib,e.<PERSON>act,e.normalizeWheel)}(this,(function(e,t,o,r){"use strict";function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(o){if("default"!==o){var r=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(t,o,r.get?r:{enumerable:!0,get:function(){return e[o]}})}})),t.default=e,Object.freeze(t)}var a=n(o),s=i(r);function c(e,t,o,r,i,n){void 0===n&&(n=0);var a=C(e,t,n),s=a.width,c=a.height,h=Math.min(s,o),p=Math.min(c,r);return h>p*i?{width:p*i,height:p}:{width:h,height:h/i}}function h(e,t,o,r,i){void 0===i&&(i=0);var n=C(t.width,t.height,i),a=n.width,s=n.height;return{x:p(e.x,a,o.width,r),y:p(e.y,s,o.height,r)}}function p(e,t,o,r){var i=t*r/2-o/2;return y(e,-i,i)}function u(e,t){return Math.sqrt(Math.pow(e.y-t.y,2)+Math.pow(e.x-t.x,2))}function d(e,t){return 180*Math.atan2(t.y-e.y,t.x-e.x)/Math.PI}function l(e,o,r,i,n,a,s){void 0===a&&(a=0),void 0===s&&(s=!0);var c=s?f:v,h=C(o.width,o.height,a),p=C(o.naturalWidth,o.naturalHeight,a),u={x:c(100,((h.width-r.width/n)/2-e.x/n)/h.width*100),y:c(100,((h.height-r.height/n)/2-e.y/n)/h.height*100),width:c(100,r.width/h.width*100/n),height:c(100,r.height/h.height*100/n)},d=Math.round(c(p.width,u.width*p.width/100)),l=Math.round(c(p.height,u.height*p.height/100)),g=p.width>=p.height*i?{width:Math.round(l*i),height:l}:{width:d,height:Math.round(d/i)};return{croppedAreaPercentages:u,croppedAreaPixels:t.__assign(t.__assign({},g),{x:Math.round(c(p.width-g.width,u.x*p.width/100)),y:Math.round(c(p.height-g.height,u.y*p.height/100))})}}function f(e,t){return Math.min(e,Math.max(0,t))}function v(e,t){return t}function g(e,t,o,r,i,n){var a=C(t.width,t.height,o),s=y(r.width/a.width*(100/e.width),i,n);return{crop:{x:s*a.width/2-r.width/2-a.width*s*(e.x/100),y:s*a.height/2-r.height/2-a.height*s*(e.y/100)},zoom:s}}function m(e,t,o,r,i,n){void 0===o&&(o=0);var a=C(t.naturalWidth,t.naturalHeight,o),s=y(function(e,t,o){var r=function(e){return e.width>e.height?e.width/e.naturalWidth:e.height/e.naturalHeight}(t);return o.height>o.width?o.height/(e.height*r):o.width/(e.width*r)}(e,t,r),i,n),c=r.height>r.width?r.height/e.height:r.width/e.width;return{crop:{x:((a.width-e.width)/2-e.x)*c,y:((a.height-e.height)/2-e.y)*c},zoom:s}}function w(e,t){return{x:(t.x+e.x)/2,y:(t.y+e.y)/2}}function C(e,t,o){var r=o*Math.PI/180;return{width:Math.abs(Math.cos(r)*e)+Math.abs(Math.sin(r)*t),height:Math.abs(Math.sin(r)*e)+Math.abs(Math.cos(r)*t)}}function y(e,t,o){return Math.min(Math.max(e,t),o)}function S(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.filter((function(e){return"string"==typeof e&&e.length>0})).join(" ").trim()}var R=function(e){function o(){var r=null!==e&&e.apply(this,arguments)||this;return r.cropperRef=a.createRef(),r.imageRef=a.createRef(),r.videoRef=a.createRef(),r.containerPosition={x:0,y:0},r.containerRef=null,r.styleRef=null,r.containerRect=null,r.mediaSize={width:0,height:0,naturalWidth:0,naturalHeight:0},r.dragStartPosition={x:0,y:0},r.dragStartCrop={x:0,y:0},r.gestureZoomStart=0,r.gestureRotationStart=0,r.isTouching=!1,r.lastPinchDistance=0,r.lastPinchRotation=0,r.rafDragTimeout=null,r.rafPinchTimeout=null,r.wheelTimer=null,r.currentDoc="undefined"!=typeof document?document:null,r.currentWindow="undefined"!=typeof window?window:null,r.resizeObserver=null,r.state={cropSize:null,hasWheelJustStarted:!1,mediaObjectFit:void 0},r.initResizeObserver=function(){if(void 0!==window.ResizeObserver&&r.containerRef){var e=!0;r.resizeObserver=new window.ResizeObserver((function(t){e?e=!1:r.computeSizes()})),r.resizeObserver.observe(r.containerRef)}},r.preventZoomSafari=function(e){return e.preventDefault()},r.cleanEvents=function(){r.currentDoc&&(r.currentDoc.removeEventListener("mousemove",r.onMouseMove),r.currentDoc.removeEventListener("mouseup",r.onDragStopped),r.currentDoc.removeEventListener("touchmove",r.onTouchMove),r.currentDoc.removeEventListener("touchend",r.onDragStopped),r.currentDoc.removeEventListener("gesturechange",r.onGestureChange),r.currentDoc.removeEventListener("gestureend",r.onGestureEnd),r.currentDoc.removeEventListener("scroll",r.onScroll))},r.clearScrollEvent=function(){r.containerRef&&r.containerRef.removeEventListener("wheel",r.onWheel),r.wheelTimer&&clearTimeout(r.wheelTimer)},r.onMediaLoad=function(){var e=r.computeSizes();e&&(r.emitCropData(),r.setInitialCrop(e)),r.props.onMediaLoaded&&r.props.onMediaLoaded(r.mediaSize)},r.setInitialCrop=function(e){if(r.props.initialCroppedAreaPercentages){var t=g(r.props.initialCroppedAreaPercentages,r.mediaSize,r.props.rotation,e,r.props.minZoom,r.props.maxZoom),o=t.crop,i=t.zoom;r.props.onCropChange(o),r.props.onZoomChange&&r.props.onZoomChange(i)}else if(r.props.initialCroppedAreaPixels){var n=m(r.props.initialCroppedAreaPixels,r.mediaSize,r.props.rotation,e,r.props.minZoom,r.props.maxZoom);o=n.crop,i=n.zoom;r.props.onCropChange(o),r.props.onZoomChange&&r.props.onZoomChange(i)}},r.computeSizes=function(){var e,o,i,n,a,s,h=r.imageRef.current||r.videoRef.current;if(h&&r.containerRef){r.containerRect=r.containerRef.getBoundingClientRect(),r.saveContainerPosition();var p=r.containerRect.width/r.containerRect.height,u=(null===(e=r.imageRef.current)||void 0===e?void 0:e.naturalWidth)||(null===(o=r.videoRef.current)||void 0===o?void 0:o.videoWidth)||0,d=(null===(i=r.imageRef.current)||void 0===i?void 0:i.naturalHeight)||(null===(n=r.videoRef.current)||void 0===n?void 0:n.videoHeight)||0,l=u/d,f=void 0;if(h.offsetWidth<u||h.offsetHeight<d)switch(r.state.mediaObjectFit){default:case"contain":f=p>l?{width:r.containerRect.height*l,height:r.containerRect.height}:{width:r.containerRect.width,height:r.containerRect.width/l};break;case"horizontal-cover":f={width:r.containerRect.width,height:r.containerRect.width/l};break;case"vertical-cover":f={width:r.containerRect.height*l,height:r.containerRect.height}}else f={width:h.offsetWidth,height:h.offsetHeight};r.mediaSize=t.__assign(t.__assign({},f),{naturalWidth:u,naturalHeight:d}),r.props.setMediaSize&&r.props.setMediaSize(r.mediaSize);var v=r.props.cropSize?r.props.cropSize:c(r.mediaSize.width,r.mediaSize.height,r.containerRect.width,r.containerRect.height,r.props.aspect,r.props.rotation);return(null===(a=r.state.cropSize)||void 0===a?void 0:a.height)===v.height&&(null===(s=r.state.cropSize)||void 0===s?void 0:s.width)===v.width||r.props.onCropSizeChange&&r.props.onCropSizeChange(v),r.setState({cropSize:v},r.recomputeCropPosition),r.props.setCropSize&&r.props.setCropSize(v),v}},r.saveContainerPosition=function(){if(r.containerRef){var e=r.containerRef.getBoundingClientRect();r.containerPosition={x:e.left,y:e.top}}},r.onMouseDown=function(e){r.currentDoc&&(e.preventDefault(),r.currentDoc.addEventListener("mousemove",r.onMouseMove),r.currentDoc.addEventListener("mouseup",r.onDragStopped),r.saveContainerPosition(),r.onDragStart(o.getMousePoint(e)))},r.onMouseMove=function(e){return r.onDrag(o.getMousePoint(e))},r.onScroll=function(e){r.currentDoc&&(e.preventDefault(),r.saveContainerPosition())},r.onTouchStart=function(e){r.currentDoc&&(r.isTouching=!0,r.props.onTouchRequest&&!r.props.onTouchRequest(e)||(r.currentDoc.addEventListener("touchmove",r.onTouchMove,{passive:!1}),r.currentDoc.addEventListener("touchend",r.onDragStopped),r.saveContainerPosition(),2===e.touches.length?r.onPinchStart(e):1===e.touches.length&&r.onDragStart(o.getTouchPoint(e.touches[0]))))},r.onTouchMove=function(e){e.preventDefault(),2===e.touches.length?r.onPinchMove(e):1===e.touches.length&&r.onDrag(o.getTouchPoint(e.touches[0]))},r.onGestureStart=function(e){r.currentDoc&&(e.preventDefault(),r.currentDoc.addEventListener("gesturechange",r.onGestureChange),r.currentDoc.addEventListener("gestureend",r.onGestureEnd),r.gestureZoomStart=r.props.zoom,r.gestureRotationStart=r.props.rotation)},r.onGestureChange=function(e){if(e.preventDefault(),!r.isTouching){var t=o.getMousePoint(e),i=r.gestureZoomStart-1+e.scale;if(r.setNewZoom(i,t,{shouldUpdatePosition:!0}),r.props.onRotationChange){var n=r.gestureRotationStart+e.rotation;r.props.onRotationChange(n)}}},r.onGestureEnd=function(e){r.cleanEvents()},r.onDragStart=function(e){var o,i,n=e.x,a=e.y;r.dragStartPosition={x:n,y:a},r.dragStartCrop=t.__assign({},r.props.crop),null===(i=(o=r.props).onInteractionStart)||void 0===i||i.call(o)},r.onDrag=function(e){var t=e.x,o=e.y;r.currentWindow&&(r.rafDragTimeout&&r.currentWindow.cancelAnimationFrame(r.rafDragTimeout),r.rafDragTimeout=r.currentWindow.requestAnimationFrame((function(){if(r.state.cropSize&&void 0!==t&&void 0!==o){var e=t-r.dragStartPosition.x,i=o-r.dragStartPosition.y,n={x:r.dragStartCrop.x+e,y:r.dragStartCrop.y+i},a=r.props.restrictPosition?h(n,r.mediaSize,r.state.cropSize,r.props.zoom,r.props.rotation):n;r.props.onCropChange(a)}})))},r.onDragStopped=function(){var e,t;r.isTouching=!1,r.cleanEvents(),r.emitCropData(),null===(t=(e=r.props).onInteractionEnd)||void 0===t||t.call(e)},r.onWheel=function(e){if(r.currentWindow&&(!r.props.onWheelRequest||r.props.onWheelRequest(e))){e.preventDefault();var t=o.getMousePoint(e),i=s.default(e).pixelY,n=r.props.zoom-i*r.props.zoomSpeed/200;r.setNewZoom(n,t,{shouldUpdatePosition:!0}),r.state.hasWheelJustStarted||r.setState({hasWheelJustStarted:!0},(function(){var e,t;return null===(t=(e=r.props).onInteractionStart)||void 0===t?void 0:t.call(e)})),r.wheelTimer&&clearTimeout(r.wheelTimer),r.wheelTimer=r.currentWindow.setTimeout((function(){return r.setState({hasWheelJustStarted:!1},(function(){var e,t;return null===(t=(e=r.props).onInteractionEnd)||void 0===t?void 0:t.call(e)}))}),250)}},r.getPointOnContainer=function(e,t){var o=e.x,i=e.y;if(!r.containerRect)throw new Error("The Cropper is not mounted");return{x:r.containerRect.width/2-(o-t.x),y:r.containerRect.height/2-(i-t.y)}},r.getPointOnMedia=function(e){var t=e.x,o=e.y,i=r.props,n=i.crop,a=i.zoom;return{x:(t+n.x)/a,y:(o+n.y)/a}},r.setNewZoom=function(e,t,o){var i=(void 0===o?{}:o).shouldUpdatePosition,n=void 0===i||i;if(r.state.cropSize&&r.props.onZoomChange){var a=y(e,r.props.minZoom,r.props.maxZoom);if(n){var s=r.getPointOnContainer(t,r.containerPosition),c=r.getPointOnMedia(s),p={x:c.x*a-s.x,y:c.y*a-s.y},u=r.props.restrictPosition?h(p,r.mediaSize,r.state.cropSize,a,r.props.rotation):p;r.props.onCropChange(u)}r.props.onZoomChange(a)}},r.getCropData=function(){return r.state.cropSize?l(r.props.restrictPosition?h(r.props.crop,r.mediaSize,r.state.cropSize,r.props.zoom,r.props.rotation):r.props.crop,r.mediaSize,r.state.cropSize,r.getAspect(),r.props.zoom,r.props.rotation,r.props.restrictPosition):null},r.emitCropData=function(){var e=r.getCropData();if(e){var t=e.croppedAreaPercentages,o=e.croppedAreaPixels;r.props.onCropComplete&&r.props.onCropComplete(t,o),r.props.onCropAreaChange&&r.props.onCropAreaChange(t,o)}},r.emitCropAreaChange=function(){var e=r.getCropData();if(e){var t=e.croppedAreaPercentages,o=e.croppedAreaPixels;r.props.onCropAreaChange&&r.props.onCropAreaChange(t,o)}},r.recomputeCropPosition=function(){if(r.state.cropSize){var e=r.props.restrictPosition?h(r.props.crop,r.mediaSize,r.state.cropSize,r.props.zoom,r.props.rotation):r.props.crop;r.props.onCropChange(e),r.emitCropData()}},r.onKeyDown=function(e){var o,i,n=r.props,a=n.crop,s=n.onCropChange,c=n.keyboardStep,p=n.zoom,u=n.rotation,d=c;if(r.state.cropSize){e.shiftKey&&(d*=.2);var l=t.__assign({},a);switch(e.key){case"ArrowUp":l.y-=d,e.preventDefault();break;case"ArrowDown":l.y+=d,e.preventDefault();break;case"ArrowLeft":l.x-=d,e.preventDefault();break;case"ArrowRight":l.x+=d,e.preventDefault();break;default:return}r.props.restrictPosition&&(l=h(l,r.mediaSize,r.state.cropSize,p,u)),e.repeat||null===(i=(o=r.props).onInteractionStart)||void 0===i||i.call(o),s(l)}},r.onKeyUp=function(e){var t,o;switch(e.key){case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":e.preventDefault();break;default:return}r.emitCropData(),null===(o=(t=r.props).onInteractionEnd)||void 0===o||o.call(t)},r}return t.__extends(o,e),o.prototype.componentDidMount=function(){this.currentDoc&&this.currentWindow&&(this.containerRef&&(this.containerRef.ownerDocument&&(this.currentDoc=this.containerRef.ownerDocument),this.currentDoc.defaultView&&(this.currentWindow=this.currentDoc.defaultView),this.initResizeObserver(),void 0===window.ResizeObserver&&this.currentWindow.addEventListener("resize",this.computeSizes),this.props.zoomWithScroll&&this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}),this.containerRef.addEventListener("gesturestart",this.onGestureStart)),this.currentDoc.addEventListener("scroll",this.onScroll),this.props.disableAutomaticStylesInjection||(this.styleRef=this.currentDoc.createElement("style"),this.styleRef.setAttribute("type","text/css"),this.props.nonce&&this.styleRef.setAttribute("nonce",this.props.nonce),this.styleRef.innerHTML=".reactEasyCrop_Container {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  user-select: none;\n  touch-action: none;\n  cursor: move;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.reactEasyCrop_Image,\n.reactEasyCrop_Video {\n  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */\n}\n\n.reactEasyCrop_Contain {\n  max-width: 100%;\n  max-height: 100%;\n  margin: auto;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n}\n.reactEasyCrop_Cover_Horizontal {\n  width: 100%;\n  height: auto;\n}\n.reactEasyCrop_Cover_Vertical {\n  width: auto;\n  height: 100%;\n}\n\n.reactEasyCrop_CropArea {\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  box-sizing: border-box;\n  box-shadow: 0 0 0 9999em;\n  color: rgba(0, 0, 0, 0.5);\n  overflow: hidden;\n}\n\n.reactEasyCrop_CropAreaRound {\n  border-radius: 50%;\n}\n\n.reactEasyCrop_CropAreaGrid::before {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 0;\n  bottom: 0;\n  left: 33.33%;\n  right: 33.33%;\n  border-top: 0;\n  border-bottom: 0;\n}\n\n.reactEasyCrop_CropAreaGrid::after {\n  content: ' ';\n  box-sizing: border-box;\n  position: absolute;\n  border: 1px solid rgba(255, 255, 255, 0.5);\n  top: 33.33%;\n  bottom: 33.33%;\n  left: 0;\n  right: 0;\n  border-left: 0;\n  border-right: 0;\n}\n",this.currentDoc.head.appendChild(this.styleRef)),this.imageRef.current&&this.imageRef.current.complete&&this.onMediaLoad(),this.props.setImageRef&&this.props.setImageRef(this.imageRef),this.props.setVideoRef&&this.props.setVideoRef(this.videoRef),this.props.setCropperRef&&this.props.setCropperRef(this.cropperRef))},o.prototype.componentWillUnmount=function(){var e,t;this.currentDoc&&this.currentWindow&&(void 0===window.ResizeObserver&&this.currentWindow.removeEventListener("resize",this.computeSizes),null===(e=this.resizeObserver)||void 0===e||e.disconnect(),this.containerRef&&this.containerRef.removeEventListener("gesturestart",this.preventZoomSafari),this.styleRef&&(null===(t=this.styleRef.parentNode)||void 0===t||t.removeChild(this.styleRef)),this.cleanEvents(),this.props.zoomWithScroll&&this.clearScrollEvent())},o.prototype.componentDidUpdate=function(e){var t,o,r,i,n,a,s,c,h;e.rotation!==this.props.rotation?(this.computeSizes(),this.recomputeCropPosition()):e.aspect!==this.props.aspect||e.objectFit!==this.props.objectFit?this.computeSizes():e.zoom!==this.props.zoom?this.recomputeCropPosition():(null===(t=e.cropSize)||void 0===t?void 0:t.height)!==(null===(o=this.props.cropSize)||void 0===o?void 0:o.height)||(null===(r=e.cropSize)||void 0===r?void 0:r.width)!==(null===(i=this.props.cropSize)||void 0===i?void 0:i.width)?this.computeSizes():(null===(n=e.crop)||void 0===n?void 0:n.x)===(null===(a=this.props.crop)||void 0===a?void 0:a.x)&&(null===(s=e.crop)||void 0===s?void 0:s.y)===(null===(c=this.props.crop)||void 0===c?void 0:c.y)||this.emitCropAreaChange(),e.zoomWithScroll!==this.props.zoomWithScroll&&this.containerRef&&(this.props.zoomWithScroll?this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}):this.clearScrollEvent()),e.video!==this.props.video&&(null===(h=this.videoRef.current)||void 0===h||h.load());var p=this.getObjectFit();p!==this.state.mediaObjectFit&&this.setState({mediaObjectFit:p},this.computeSizes)},o.prototype.getAspect=function(){var e=this.props,t=e.cropSize,o=e.aspect;return t?t.width/t.height:o},o.prototype.getObjectFit=function(){var e,t,o,r;if("cover"===this.props.objectFit){if((this.imageRef.current||this.videoRef.current)&&this.containerRef){this.containerRect=this.containerRef.getBoundingClientRect();var i=this.containerRect.width/this.containerRect.height;return((null===(e=this.imageRef.current)||void 0===e?void 0:e.naturalWidth)||(null===(t=this.videoRef.current)||void 0===t?void 0:t.videoWidth)||0)/((null===(o=this.imageRef.current)||void 0===o?void 0:o.naturalHeight)||(null===(r=this.videoRef.current)||void 0===r?void 0:r.videoHeight)||0)<i?"horizontal-cover":"vertical-cover"}return"horizontal-cover"}return this.props.objectFit},o.prototype.onPinchStart=function(e){var t=o.getTouchPoint(e.touches[0]),r=o.getTouchPoint(e.touches[1]);this.lastPinchDistance=u(t,r),this.lastPinchRotation=d(t,r),this.onDragStart(w(t,r))},o.prototype.onPinchMove=function(e){var t=this;if(this.currentDoc&&this.currentWindow){var r=o.getTouchPoint(e.touches[0]),i=o.getTouchPoint(e.touches[1]),n=w(r,i);this.onDrag(n),this.rafPinchTimeout&&this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout),this.rafPinchTimeout=this.currentWindow.requestAnimationFrame((function(){var e=u(r,i),o=t.props.zoom*(e/t.lastPinchDistance);t.setNewZoom(o,n,{shouldUpdatePosition:!1}),t.lastPinchDistance=e;var a=d(r,i),s=t.props.rotation+(a-t.lastPinchRotation);t.props.onRotationChange&&t.props.onRotationChange(s),t.lastPinchRotation=a}))}},o.prototype.render=function(){var e,o=this,r=this.props,i=r.image,n=r.video,s=r.mediaProps,c=r.cropperProps,h=r.transform,p=r.crop,u=p.x,d=p.y,l=r.rotation,f=r.zoom,v=r.cropShape,g=r.showGrid,m=r.roundCropAreaPixels,w=r.style,C=w.containerStyle,y=w.cropAreaStyle,R=w.mediaStyle,z=r.classes,b=z.containerClassName,P=z.cropAreaClassName,D=z.mediaClassName,x=null!==(e=this.state.mediaObjectFit)&&void 0!==e?e:this.getObjectFit();return a.createElement("div",{onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,ref:function(e){return o.containerRef=e},"data-testid":"container",style:C,className:S("reactEasyCrop_Container",b)},i?a.createElement("img",t.__assign({alt:"",className:S("reactEasyCrop_Image","contain"===x&&"reactEasyCrop_Contain","horizontal-cover"===x&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===x&&"reactEasyCrop_Cover_Vertical",D)},s,{src:i,ref:this.imageRef,style:t.__assign(t.__assign({},R),{transform:h||"translate(".concat(u,"px, ").concat(d,"px) rotate(").concat(l,"deg) scale(").concat(f,")")}),onLoad:this.onMediaLoad})):n&&a.createElement("video",t.__assign({autoPlay:!0,playsInline:!0,loop:!0,muted:!0,className:S("reactEasyCrop_Video","contain"===x&&"reactEasyCrop_Contain","horizontal-cover"===x&&"reactEasyCrop_Cover_Horizontal","vertical-cover"===x&&"reactEasyCrop_Cover_Vertical",D)},s,{ref:this.videoRef,onLoadedMetadata:this.onMediaLoad,style:t.__assign(t.__assign({},R),{transform:h||"translate(".concat(u,"px, ").concat(d,"px) rotate(").concat(l,"deg) scale(").concat(f,")")}),controls:!1}),(Array.isArray(n)?n:[{src:n}]).map((function(e){return a.createElement("source",t.__assign({key:e.src},e))}))),this.state.cropSize&&a.createElement("div",t.__assign({ref:this.cropperRef,style:t.__assign(t.__assign({},y),{width:m?Math.round(this.state.cropSize.width):this.state.cropSize.width,height:m?Math.round(this.state.cropSize.height):this.state.cropSize.height}),tabIndex:0,onKeyDown:this.onKeyDown,onKeyUp:this.onKeyUp,"data-testid":"cropper",className:S("reactEasyCrop_CropArea","round"===v&&"reactEasyCrop_CropAreaRound",g&&"reactEasyCrop_CropAreaGrid",P)},c)))},o.defaultProps={zoom:1,rotation:0,aspect:4/3,maxZoom:3,minZoom:1,cropShape:"rect",objectFit:"contain",showGrid:!0,style:{},classes:{},mediaProps:{},cropperProps:{},zoomSpeed:1,restrictPosition:!0,zoomWithScroll:!0,keyboardStep:1},o.getMousePoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},o.getTouchPoint=function(e){return{x:Number(e.clientX),y:Number(e.clientY)}},o}(a.Component);e.default=R,e.getInitialCropFromCroppedAreaPercentages=g,e.getInitialCropFromCroppedAreaPixels=m,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react-easy-crop.min.js.map
