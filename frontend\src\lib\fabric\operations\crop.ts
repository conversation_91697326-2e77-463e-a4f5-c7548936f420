import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";
import { loadCanvasImage } from "../rendering/image";

// Create a DOMRect-like object from crop dimensions
export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

// Calculate scaled image dimensions
export const getScaledImageDimensions = (canvas: Canvas) => {
  const backgroundImage = canvas.backgroundImage;
  if (!backgroundImage) {
    return {
      originalWidth: 512,
      originalHeight: 512,
      scale: 1,
      scaledWidth: 512,
      scaledHeight: 512,
    };
  }

  const originalWidth = backgroundImage.width || 512;
  const originalHeight = backgroundImage.height || 512;
  const scale = backgroundImage.scaleX || 1;
  const scaledWidth = originalWidth * scale;
  const scaledHeight = originalHeight * scale;

  return {
    originalWidth,
    originalHeight,
    scale,
    scaledWidth,
    scaledHeight,
  };
};

// Calculate crop rectangle coordinates in canvas space
export const calculateCropCoordinates = (
  cropData: CropData,
  scaledWidth: number,
  scaledHeight: number
) => {
  if (!cropData.normalizedCropRect) {
    throw new Error("Normalized crop rect is required");
  }

  return {
    left: cropData.normalizedCropRect.left * scaledWidth,
    top: cropData.normalizedCropRect.top * scaledHeight,
    width: cropData.normalizedCropRect.width * scaledWidth,
    height: cropData.normalizedCropRect.height * scaledHeight,
  };
};

// Create a clip rectangle for cropping
export const createClipRect = (left: number, top: number, width: number, height: number): Rect => {
  return new Rect({
    left,
    top,
    width,
    height,
    absolutePositioned: true,
    selectable: false,
    evented: false,
  });
};

// Calculate viewport transform for crop display
export const calculateCropViewportTransform = (
  canvasWidth: number,
  canvasHeight: number,
  cropLeft: number,
  cropTop: number,
  cropWidth: number,
  cropHeight: number
): [number, number, number, number, number, number] => {
  // Use uniform scaling to maintain aspect ratio
  const scale = Math.min(canvasWidth / cropWidth, canvasHeight / cropHeight);

  return [scale, 0, 0, scale, -cropLeft * scale, -cropTop * scale];
};

// Apply crop transformation to canvas
export const applyCropTransformation = (
  canvas: Canvas,
  left: number,
  top: number,
  width: number,
  height: number,
  containerWidth: number,
  containerHeight: number
): void => {
  const scale = Math.min(containerWidth / width, containerHeight / height);
  const actualWidth = width * scale;
  const actualHeight = height * scale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });

  const vpt: [number, number, number, number, number, number] = [
    scale,
    0,
    0,
    scale,
    -left * scale,
    -top * scale,
  ];

  canvas.setViewportTransform(vpt);
};

// Apply crop to canvas with all necessary transformations
export const applyCropToCanvas = (
  canvas: Canvas,
  cropData: CropData,
  containerRect?: DOMRect
): void => {
  if (!cropData.normalizedCropRect) return;

  const { scaledWidth, scaledHeight } = getScaledImageDimensions(canvas);
  const { left, top, width, height } = calculateCropCoordinates(
    cropData,
    scaledWidth,
    scaledHeight
  );

  const viewportContainer = cropData.canvasDimensions || containerRect;
  if (viewportContainer) {
    applyCropTransformation(
      canvas,
      left,
      top,
      width,
      height,
      viewportContainer.width,
      viewportContainer.height
    );
  }

  canvas.renderAll();
};

// Handle crop operation
export const handleCropOperation = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        containerRef,
        originalImageUrl
      );
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    // Get the crop rectangle coordinates - use the actual rectangle dimensions
    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    // Remove only the crop rectangle, preserve all other objects
    cropRect.set({ visible: false });
    canvas.remove(cropRect);
    canvas.discardActiveObject();

    if (containerRef?.current) {
      const containerBounds = containerRef.current.getBoundingClientRect();

      applyCropTransformation(
        canvas,
        left,
        top,
        width,
        height,
        containerBounds.width,
        containerBounds.height
      );
      canvas.renderAll();
    }

    // Get the actual rendered dimensions of the background image
    const backgroundImage = canvas.backgroundImage;
    if (!backgroundImage) return;

    const imageWidth = backgroundImage.width || 512;
    const imageHeight = backgroundImage.height || 512;
    const imageScaleX = backgroundImage.scaleX || 1;
    const imageScaleY = backgroundImage.scaleY || 1;

    // Calculate the actual rendered size of the background image
    const renderedImageWidth = imageWidth * imageScaleX;
    const renderedImageHeight = imageHeight * imageScaleY;

    // Get background image position (typically at origin for our use case)
    const backgroundImageLeft = backgroundImage.left || 0;
    const backgroundImageTop = backgroundImage.top || 0;

    // Calculate crop coordinates relative to the background image
    const relativeLeft = left - backgroundImageLeft;
    const relativeTop = top - backgroundImageTop;

    const normalizedCropRect = {
      left: relativeLeft / renderedImageWidth,
      top: relativeTop / renderedImageHeight,
      width: width / renderedImageWidth,
      height: height / renderedImageHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };
    setCropData(cropResult);

    canvas.renderAll();
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

// Restore canvas from cropped state
export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  if (!canvas) return;

  // Clear crop-related settings
  canvas.clipPath = undefined;
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

  // Set canvas dimensions
  if (containerRef?.current) {
    const containerBounds = containerRef.current.getBoundingClientRect();
    canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
  }

  // Reload the original image
  if (originalImageUrl?.current) {
    const containerRect = containerRef?.current?.getBoundingClientRect();
    await loadCanvasImage(canvas, originalImageUrl.current, {
      containerRect: containerRect || undefined,
    });
  }

  // Update state
  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });

  setHasPerformedCrop(false);

  // Render the canvas
  canvas.renderAll();
};
